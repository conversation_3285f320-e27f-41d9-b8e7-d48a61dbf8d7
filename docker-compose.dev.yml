version: '3.8'

services:
  # MongoDB service with replica set for transaction support (no authentication for development)
  mongodb:
    image: mongo:7.0
    container_name: cryptoyield-mongodb-dev
    restart: always
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    ports:
      - "27017:27017"
    networks:
      - cryptoyield-dev-network
    command: mongod --replSet rs0 --bind_ip_all --noauth
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  # MongoDB replica set initialization
  mongodb-init:
    image: mongo:7.0
    container_name: cryptoyield-mongodb-init
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - cryptoyield-dev-network
    command: >
      mongosh --host mongodb:27017 --eval "
      try {
        rs.initiate({
          _id: 'rs0',
          members: [{ _id: 0, host: 'mongodb:27017' }]
        });
        print('Replica set initialized successfully');
      } catch (e) {
        if (e.message.includes('already initialized')) {
          print('Replica set already initialized');
        } else {
          print('Error: ' + e.message);
        }
      }
      "
    restart: "no"

  # Redis service for caching and session management
  redis:
    image: redis:7-alpine
    container_name: cryptoyield-redis-dev
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - cryptoyield-dev-network
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

  # Mongo Express service (MongoDB admin interface)
  mongo-express:
    image: mongo-express:latest
    container_name: cryptoyield-mongo-express-dev
    restart: always
    depends_on:
      - mongodb
    environment:
      # MongoDB connection settings (no auth for development)
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_MONGODB_PORT: 27017

      # Basic authentication for Mongo Express web interface
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123

      # Mongo Express settings
      ME_CONFIG_MONGODB_ENABLE_ADMIN: "true"
      ME_CONFIG_OPTIONS_EDITORTHEME: ambiance

      # Connection URL (no auth for development)
      ME_CONFIG_MONGODB_URL: mongodb://mongodb:27017/
    ports:
      - "8081:8081"
    networks:
      - cryptoyield-dev-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8081"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 45s

  # Backend service with live code editing and hot-reload
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: cryptoyield-backend-dev
    restart: always
    ports:
      - "5000:5000"  # Backend runs on port 5000 internally, exposed on 5000
      - "9229:9229"  # Debug port for Node.js inspector
    working_dir: /app
    environment:
      # Development environment variables
      NODE_ENV: development
      PORT: 5000

      # MongoDB connection (no auth for development)
      MONGO_URI: mongodb://mongodb:27017/cryptoyield?replicaSet=rs0

      # Redis connection (using container network)
      REDIS_URL: redis://redis:6379
      REDIS_HOST: redis
      REDIS_PORT: 6379

      # JWT configuration
      JWT_SECRET: crypto_yield_hub_dev_jwt_secret
      JWT_EXPIRES_IN: 1d
      JWT_REFRESH_EXPIRES_IN: 7d

      # CORS
      FRONTEND_URL: http://localhost:3003
      CORS_ORIGIN: "*"

      # Blockchain
      CONTRACT_ADDRESS: ******************************************
      PROVIDER_URL: https://mainnet.infura.io/v3/********************************

      # Logging
      LOG_LEVEL: debug

      # Debug configuration
      DEBUG: "*"
      NODE_OPTIONS: "--inspect=0.0.0.0:9229"
      TS_NODE_TRANSPILE_ONLY: "true"

      # Rate limiting
      RATE_LIMIT_WINDOW_MS: 60000
      RATE_LIMIT_MAX: 1000

      # Cache
      CACHE_TTL: 300

      # Email configuration
      EMAIL_HOST: mail.shpnfinance.com
      EMAIL_PORT: 587
      EMAIL_SECURE: false
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: ThisIsPass@123
      EMAIL_FROM: <EMAIL>
    volumes:
      # Mount entire backend directory for hot reload (excluding node_modules)
      - ./backend:/app:cached
      # Use named volume for node_modules to avoid conflicts
      - backend_node_modules:/app/node_modules
      # Mount uploads directory separately to persist data
      - ./backend/uploads:/app/uploads:cached
    networks:
      - cryptoyield-dev-network
    depends_on:
      mongodb:
        condition: service_healthy
      mongodb-init:
        condition: service_completed_successfully
      redis:
        condition: service_healthy
    command: ["npm", "run", "dev:docker"]
    # Enable debugging
    stdin_open: true
    tty: true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Backend service with debug breakpoints (use with: docker-compose -f docker-compose.dev.yml --profile debug up backend-debug)
  backend-debug:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: cryptoyield-backend-debug
    restart: "no"  # Don't auto-restart when debugging
    ports:
      - "5001:5000"  # Different port to avoid conflicts
      - "9230:9229"  # Different debug port
    working_dir: /app
    environment:
      # Development environment variables (same as backend)
      NODE_ENV: development
      PORT: 5000
      MONGO_URI: mongodb://mongodb:27017/cryptoyield?replicaSet=rs0
      REDIS_URL: redis://redis:6379
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: crypto_yield_hub_dev_jwt_secret
      JWT_EXPIRES_IN: 1d
      JWT_REFRESH_EXPIRES_IN: 7d
      FRONTEND_URL: http://localhost:3003
      CORS_ORIGIN: "*"
      CONTRACT_ADDRESS: ******************************************
      PROVIDER_URL: https://mainnet.infura.io/v3/********************************
      LOG_LEVEL: debug

      # Debug configuration with breakpoints
      DEBUG: "*"
      NODE_OPTIONS: "--inspect-brk=0.0.0.0:9229"
      TS_NODE_TRANSPILE_ONLY: "true"

      RATE_LIMIT_WINDOW_MS: 60000
      RATE_LIMIT_MAX: 1000
      CACHE_TTL: 300
      EMAIL_HOST: mail.shpnfinance.com
      EMAIL_PORT: 587
      EMAIL_SECURE: false
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: ThisIsPass@123
      EMAIL_FROM: <EMAIL>
    volumes:
      - ./backend:/app:cached
      - backend_node_modules:/app/node_modules
      - ./backend/uploads:/app/uploads:cached
    networks:
      - cryptoyield-dev-network
    depends_on:
      mongodb:
        condition: service_healthy
      mongodb-init:
        condition: service_completed_successfully
      redis:
        condition: service_healthy
    command: ["npm", "run", "debug:docker"]
    # Enable debugging
    stdin_open: true
    tty: true
    profiles: ["debug"]  # Only start when explicitly requested

networks:
  cryptoyield-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
  backend_node_modules:
    driver: local
