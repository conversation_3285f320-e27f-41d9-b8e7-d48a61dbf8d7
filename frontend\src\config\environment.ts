// Environment configuration for the application
export const config = {
  // Development mode check
  isDevelopment: import.meta.env.DEV,
  
  // Production mode check
  isProduction: import.meta.env.PROD,
  
  // API configuration
  apiUrl: import.meta.env.VITE_API_URL || '/api',
  
  // Mock service configuration
  useMock: import.meta.env.VITE_USE_MOCK === 'true',
  
  // Combined check for using mock service
  shouldUseMock: import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true'
};

// Debug log in development
if (config.isDevelopment) {
  console.log('🔧 Environment Config:', config);
}

export default config;
