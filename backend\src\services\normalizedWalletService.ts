import mongoose from 'mongoose';
import Wallet from '../models/walletModel';
import WalletDetail from '../models/walletDetailModel';
import { logger } from '../utils/logger';

interface AddressData {
  address: string;
  network: string;
  isDefault?: boolean;
  privateKey?: string;
  label?: string;
}

interface WalletWithAddresses {
  wallet: any;
  addresses: any[];
}

class NormalizedWalletService {
  private isDualWriteEnabled = true; // Feature flag

  /**
   * Create or update wallet with dual-write to both models
   */
  async createOrUpdateWallet(
    userId: string,
    symbol: string,
    addressData: AddressData
  ): Promise<WalletWithAddresses> {
    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // 1. Update/Create in Wallet model (existing logic)
        let wallet = await Wallet.findOne({ userId }).session(session);
        
        if (!wallet) {
          wallet = new Wallet({
            userId,
            assets: [],
            totalCommissionEarned: 0,
            totalInterestEarned: 0
          });
        }

        // Find or create asset
        let asset = wallet.assets.find((a: any) => a.symbol === symbol.toUpperCase());
        if (!asset) {
          asset = {
            symbol: symbol.toUpperCase(),
            balance: 0,
            commissionBalance: 0,
            interestBalance: 0,
            mode: 'commission',
            network: addressData.network,
            address: addressData.address,
            addresses: []
          };
          wallet.assets.push(asset);
        }

        // Add address to embedded array
        if (!asset.addresses) asset.addresses = [];
        
        // Check if address already exists
        const existingAddress = asset.addresses.find((addr: any) => addr.address === addressData.address);
        if (!existingAddress) {
          // If setting as default, unset others
          if (addressData.isDefault) {
            asset.addresses.forEach((addr: any) => { addr.isDefault = false; });
          }

          asset.addresses.push({
            address: addressData.address,
            network: addressData.network,
            isDefault: addressData.isDefault || asset.addresses.length === 0,
            isActive: true,
            privateKey: addressData.privateKey,
            label: addressData.label,
            lastUpdated: new Date(),
            withdrawalEnabled: true,
            addressIndex: asset.addresses.length
          });
        }

        await wallet.save({ session });

        // 2. Dual-write to WalletDetail model
        let walletDetail = null;
        if (this.isDualWriteEnabled) {
          try {
            walletDetail = await WalletDetail.findOne({
              address: addressData.address
            }).session(session);

            if (!walletDetail) {
              walletDetail = await WalletDetail.createAddress({
                walletId: wallet._id,
                userId: wallet.userId,
                symbol: symbol.toUpperCase(),
                address: addressData.address,
                network: addressData.network,
                isDefault: addressData.isDefault || false
              });
            }
          } catch (error) {
            logger.warn('Dual-write to WalletDetail failed:', error);
            // Continue without failing the main operation
          }
        }

        // 3. Get all addresses for response
        const addresses = await this.getAddresses(userId, symbol);

        return { wallet, addresses };
      });
    } finally {
      await session.endSession();
    }
  }

  /**
   * Get addresses with fallback logic (WalletDetail first, then embedded)
   */
  async getAddresses(userId: string, symbol?: string): Promise<any[]> {
    try {
      // Try WalletDetail first
      const walletDetails = await WalletDetail.findByUser(userId, symbol);
      
      if (walletDetails.length > 0) {
        logger.debug(`Found ${walletDetails.length} addresses in WalletDetail for user ${userId}`);
        return walletDetails.map(detail => ({
          _id: detail._id,
          address: detail.address,
          network: detail.network,
          isDefault: detail.isDefault,
          isActive: detail.isActive,
          symbol: detail.symbol,
          label: detail.label,
          lastUpdated: detail.lastUpdated,
          withdrawalEnabled: detail.withdrawalEnabled,
          addressIndex: detail.addressIndex,
          source: 'normalized'
        }));
      }

      // Fallback to embedded addresses
      const wallet = await Wallet.findOne({ userId });
      if (!wallet) return [];

      const addresses: any[] = [];
      for (const asset of wallet.assets) {
        if (symbol && asset.symbol !== symbol.toUpperCase()) continue;
        
        if (asset.addresses) {
          asset.addresses.forEach((addr: any) => {
            addresses.push({
              _id: addr._id,
              address: addr.address,
              network: addr.network,
              isDefault: addr.isDefault,
              isActive: addr.isActive,
              symbol: asset.symbol,
              label: addr.label,
              lastUpdated: addr.lastUpdated,
              withdrawalEnabled: addr.withdrawalEnabled,
              addressIndex: addr.addressIndex,
              source: 'embedded'
            });
          });
        }
      }

      logger.debug(`Found ${addresses.length} addresses in embedded format for user ${userId}`);
      return addresses;
    } catch (error) {
      logger.error('Error getting addresses:', error);
      return [];
    }
  }

  /**
   * Find wallet by address with dual-model support
   */
  async findWalletByAddress(address: string): Promise<any> {
    try {
      // Try WalletDetail first (faster)
      const walletDetail = await WalletDetail.findByAddress(address);
      if (walletDetail) {
        const wallet = await Wallet.findById(walletDetail.walletId);
        return {
          wallet,
          address: walletDetail,
          source: 'normalized'
        };
      }

      // Fallback to embedded search
      const wallet = await Wallet.findOne({
        'assets.addresses.address': address,
        'assets.addresses.isActive': true
      });

      if (wallet) {
        // Find the specific address
        let foundAddress = null;
        for (const asset of wallet.assets) {
          if (asset.addresses) {
            foundAddress = asset.addresses.find((addr: any) => 
              addr.address === address && addr.isActive
            );
            if (foundAddress) {
              foundAddress.symbol = asset.symbol;
              break;
            }
          }
        }

        return {
          wallet,
          address: foundAddress,
          source: 'embedded'
        };
      }

      return null;
    } catch (error) {
      logger.error('Error finding wallet by address:', error);
      return null;
    }
  }

  /**
   * Update address with dual-write
   */
  async updateAddress(
    userId: string,
    addressId: string,
    updates: Partial<AddressData>
  ): Promise<boolean> {
    const session = await mongoose.startSession();

    try {
      return await session.withTransaction(async () => {
        // 1. Update in WalletDetail if exists
        const walletDetail = await WalletDetail.findOne({
          _id: addressId,
          userId
        }).session(session);

        if (walletDetail) {
          Object.assign(walletDetail, updates, { lastUpdated: new Date() });
          await walletDetail.save({ session });
        }

        // 2. Update in embedded addresses
        const wallet = await Wallet.findOne({ userId }).session(session);
        if (wallet) {
          let updated = false;
          for (const asset of wallet.assets) {
            if (asset.addresses) {
              const addressIndex = asset.addresses.findIndex((addr: any) => 
                addr._id.toString() === addressId
              );
              if (addressIndex !== -1) {
                Object.assign(asset.addresses[addressIndex], updates, { 
                  lastUpdated: new Date() 
                });
                updated = true;
                break;
              }
            }
          }
          if (updated) {
            await wallet.save({ session });
          }
        }

        return true;
      });
    } catch (error) {
      logger.error('Error updating address:', error);
      return false;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Delete address with dual-write
   */
  async deleteAddress(userId: string, addressId: string): Promise<boolean> {
    const session = await mongoose.startSession();

    try {
      return await session.withTransaction(async () => {
        // 1. Deactivate in WalletDetail
        const walletDetail = await WalletDetail.findOne({
          _id: addressId,
          userId
        }).session(session);

        if (walletDetail) {
          await walletDetail.deactivate();
        }

        // 2. Deactivate in embedded addresses
        const wallet = await Wallet.findOne({ userId }).session(session);
        if (wallet) {
          let updated = false;
          for (const asset of wallet.assets) {
            if (asset.addresses) {
              const address = asset.addresses.find((addr: any) => 
                addr._id.toString() === addressId
              );
              if (address) {
                address.isActive = false;
                address.lastUpdated = new Date();
                updated = true;
                break;
              }
            }
          }
          if (updated) {
            await wallet.save({ session });
          }
        }

        return true;
      });
    } catch (error) {
      logger.error('Error deleting address:', error);
      return false;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(): Promise<{
    normalizedCount: number;
    embeddedCount: number;
    migrationProgress: number;
  }> {
    try {
      const normalizedCount = await WalletDetail.countDocuments({ isActive: true });
      
      const embeddedResult = await Wallet.aggregate([
        { $unwind: '$assets' },
        { $unwind: '$assets.addresses' },
        { $match: { 'assets.addresses.isActive': true } },
        { $count: 'total' }
      ]);
      
      const embeddedCount = embeddedResult[0]?.total || 0;
      const totalCount = normalizedCount + embeddedCount;
      const migrationProgress = totalCount > 0 ? (normalizedCount / totalCount) * 100 : 0;

      return {
        normalizedCount,
        embeddedCount,
        migrationProgress: Math.round(migrationProgress)
      };
    } catch (error) {
      logger.error('Error getting performance metrics:', error);
      return { normalizedCount: 0, embeddedCount: 0, migrationProgress: 0 };
    }
  }

  /**
   * Enable/disable dual-write mode
   */
  setDualWriteMode(enabled: boolean): void {
    this.isDualWriteEnabled = enabled;
    logger.info(`Dual-write mode ${enabled ? 'enabled' : 'disabled'}`);
  }
}

export default new NormalizedWalletService();
