import { useState, useEffect, useCallback } from 'react';
import { cryptoPriceService, CryptoPriceData } from '../services/cryptoPriceService';
import { getUSDRateSync } from '../utils/exchangeRates';

interface CryptoPricesState {
  prices: CryptoPriceData;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface UseCryptoPricesReturn extends CryptoPricesState {
  refreshPrices: () => Promise<void>;
  getPrice: (symbol: string) => number;
  convertToUSD: (amount: number, symbol: string) => number;
  convertFromUSD: (usdAmount: number, symbol: string) => number;
  cacheInfo: {
    hasCache: boolean;
    cacheAge: number;
    isValid: boolean;
    priceCount: number;
  };
}

/**
 * Custom hook for managing cryptocurrency prices with smart caching
 * Uses cryptoPriceService for localStorage caching and automatic updates
 */
export const useCryptoPrices = (): UseCryptoPricesReturn => {
  const [state, setState] = useState<CryptoPricesState>({
    prices: {},
    isLoading: true,
    error: null,
    lastUpdated: null,
  });

  /**
   * Load prices on mount and subscribe to updates
   */
  useEffect(() => {
    let mounted = true;

    const loadPrices = async () => {
      try {
        setState(prev => ({ ...prev, isLoading: true, error: null }));

        const priceData = await cryptoPriceService.getPrices();

        if (mounted) {
          setState({
            prices: priceData,
            isLoading: false,
            error: null,
            lastUpdated: new Date(),
          });
          console.log('🎯 useCryptoPrices: Prices loaded successfully');
        }
      } catch (err: any) {
        if (mounted) {
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: err.message || 'Failed to load crypto prices',
          }));
          console.error('🎯 useCryptoPrices: Error loading prices:', err);
        }
      }
    };

    loadPrices();

    // Subscribe to price updates from service
    const unsubscribe = cryptoPriceService.subscribe((updatedPrices) => {
      if (mounted) {
        setState(prev => ({
          ...prev,
          prices: updatedPrices,
          lastUpdated: new Date(),
        }));
        console.log('🎯 useCryptoPrices: Prices updated via subscription');
      }
    });

    return () => {
      mounted = false;
      unsubscribe();
    };
  }, []);

  /**
   * Refresh prices manually
   */
  const refreshPrices = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const priceData = await cryptoPriceService.refreshPrices();
      setState(prev => ({
        ...prev,
        prices: priceData,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      }));

      console.log('🎯 useCryptoPrices: Prices refreshed manually');
    } catch (err: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: err.message || 'Failed to refresh crypto prices',
      }));
      console.error('🎯 useCryptoPrices: Error refreshing prices:', err);
    }
  }, []);

  /**
   * Get price for a specific cryptocurrency
   */
  const getPrice = useCallback((symbol: string): number => {
    const normalizedSymbol = symbol.toUpperCase();
    const apiPrice = state.prices[normalizedSymbol];
    if (apiPrice !== undefined && apiPrice > 0) {
      return apiPrice;
    }

    // Fallback to sync rate if not found in API prices
    return getUSDRateSync(symbol);
  }, [state.prices]);

  /**
   * Convert amount to USD
   */
  const convertToUSD = useCallback((amount: number, symbol: string): number => {
    const price = getPrice(symbol);
    return amount * price;
  }, [getPrice]);

  /**
   * Convert amount from USD
   */
  const convertFromUSD = useCallback((usdAmount: number, symbol: string): number => {
    const price = getPrice(symbol);
    return price > 0 ? usdAmount / price : 0;
  }, [getPrice]);

  /**
   * Get cache info
   */
  const cacheInfo = cryptoPriceService.getCacheInfo();

  return {
    ...state,
    refreshPrices,
    getPrice,
    convertToUSD,
    convertFromUSD,
    cacheInfo,
  };
};

/**
 * Hook for getting a single cryptocurrency price
 * Optimized for components that only need one price
 */
export const useCryptoPrice = (symbol: string) => {
  const { prices, isLoading, error, getPrice } = useCryptoPrices();

  return {
    price: getPrice(symbol),
    isLoading,
    error,
    symbol: symbol.toUpperCase()
  };
};

/**
 * Hook for crypto conversion utilities
 */
export const useCryptoConverter = () => {
  const { convertToUSD, convertFromUSD, isLoading, error } = useCryptoPrices();

  return {
    convertToUSD,
    convertFromUSD,
    isLoading,
    error
  };
};

export default useCryptoPrices;
