# 🧪 **WITHDRAWAL RACE CONDITION TESTING GUIDE**

## 📋 **OVERVIEW**

Comprehensive testing suite để kiểm tra race conditions và data integrity issues trong withdrawal operations. Tests này giúp đảm bảo hệ thống xử lý concurrent withdrawals một cách an toàn và chính xác.

---

## 🎯 **MỤC TIÊU TESTING**

### **🔍 Race Condition Detection:**
- **Concurrent withdrawal requests** cùng một thời điểm
- **Database transaction conflicts** và data corruption
- **Wallet balance inconsistencies** sau concurrent operations
- **MongoDB session handling** và transaction safety
- **API endpoint thread safety** và request handling

### **🛡️ Data Integrity Verification:**
- **Balance calculations** accuracy under concurrent load
- **Transaction record** completeness và consistency
- **Audit trail** integrity và traceability
- **Error handling** và recovery mechanisms
- **System state** consistency after failures

---

## 🚀 **AVAILABLE TESTS**

### **Test 1: Concurrent HTTP Withdrawal Requests** 🚀
```bash
# Test concurrent API calls to withdrawal endpoints
test-withdrawal-race-conditions.bat concurrent local
./test-withdrawal-race-conditions.sh concurrent docker
```

**What it tests:**
- **HTTP API thread safety** với multiple simultaneous requests
- **Authentication và authorization** under concurrent load
- **Request validation** và rate limiting effectiveness
- **Response consistency** và error handling
- **End-to-end workflow** từ API đến database

### **Test 2: Database Race Condition Tests** 🔄
```bash
# Test database-level concurrent operations
test-withdrawal-race-conditions.bat database local
./test-withdrawal-race-conditions.sh database docker
```

**What it tests:**
- **MongoDB transaction safety** với concurrent operations
- **Wallet balance updates** và locking mechanisms
- **Document versioning** và optimistic concurrency
- **Session management** và rollback capabilities
- **Index performance** under concurrent load

### **Test 3: Combined Test Suite** 🎯
```bash
# Run both test types sequentially
test-withdrawal-race-conditions.bat both local
./test-withdrawal-race-conditions.sh both docker
```

**What it tests:**
- **Complete system behavior** under mixed load patterns
- **Cross-layer interactions** between API và database
- **Resource contention** và performance degradation
- **Error propagation** và system recovery
- **Overall system resilience** và stability

---

## 🔧 **QUICK START**

### **Windows:**
```bash
# Run concurrent withdrawal test
test-withdrawal-race-conditions.bat concurrent local

# Run database race condition test  
test-withdrawal-race-conditions.bat database docker

# Run both tests
test-withdrawal-race-conditions.bat both local

# Show help
test-withdrawal-race-conditions.bat help
```

### **Linux/Mac:**
```bash
# Make script executable
chmod +x test-withdrawal-race-conditions.sh

# Run concurrent withdrawal test
./test-withdrawal-race-conditions.sh concurrent local

# Run database race condition test
./test-withdrawal-race-conditions.sh database docker

# Run both tests
./test-withdrawal-race-conditions.sh both local

# Show help
./test-withdrawal-race-conditions.sh help
```

### **NPM Scripts (trong backend directory):**
```bash
# Concurrent withdrawal tests
npm run test:concurrent-withdrawals:local
npm run test:concurrent-withdrawals:docker

# Database race condition tests
npm run test:db-race-conditions:local
npm run test:db-race-conditions:docker
```

---

## 📊 **TEST CONFIGURATION**

### **Concurrent Withdrawal Test Settings:**
```typescript
const testConfig = {
  baseUrl: 'http://localhost:5000',
  concurrentRequests: 5,           // Number of simultaneous requests
  withdrawalAmount: 0.01,          // Amount per withdrawal (BTC)
  cryptocurrency: 'BTC',
  walletAddress: '**********************************',
  network: 'bitcoin',
  withdrawalType: 'interest'       // Type: balance, interest, commission
};
```

### **Database Race Condition Test Settings:**
```typescript
const testConfig = {
  concurrentOperations: 10,        // Number of concurrent DB operations
  withdrawalAmount: 0.01,          // Amount per operation
  cryptocurrency: 'BTC',
  testOperations: [
    'withdrawal',                  // Withdrawal operations
    'balanceUpdate'               // Balance update operations
  ]
};
```

---

## 🧪 **TEST SCENARIOS**

### **Scenario 1: Multiple Users, Same Time** 👥
- **5 concurrent withdrawal requests** từ cùng user
- **Same withdrawal amount** và cryptocurrency
- **Identical timestamps** để maximize race condition potential
- **Expected**: Only valid withdrawals succeed, balance accurate

### **Scenario 2: Insufficient Balance Race** 💰
- **Multiple withdrawals** when balance chỉ đủ cho 1-2 requests
- **Concurrent balance checks** và deductions
- **Expected**: Only sufficient withdrawals succeed, no negative balance

### **Scenario 3: Database Transaction Conflicts** 🔄
- **Concurrent wallet updates** với MongoDB sessions
- **Transaction rollback testing** on failures
- **Document locking** và optimistic concurrency
- **Expected**: All operations atomic, no data corruption

### **Scenario 4: Mixed Operation Types** 🎯
- **Withdrawals + balance updates** simultaneously
- **Interest calculations + withdrawals** concurrently
- **Multiple cryptocurrency** operations
- **Expected**: All operations isolated, consistent final state

---

## 📈 **EXPECTED RESULTS**

### **✅ Successful Test Results:**
```
🎯 Test Summary:
   - Total requests: 5
   - Successful: 2
   - Failed: 3 (insufficient balance)
   - Success rate: 40.0%
   - Data integrity: ✅ OK

💰 Balance Analysis:
   - Expected deduction: 0.02 BTC
   - Actual deduction: 0.02 BTC  
   - Difference: 0.000000 BTC
```

### **❌ Failed Test Results (Race Condition Detected):**
```
🎯 Test Summary:
   - Total requests: 5
   - Successful: 5
   - Failed: 0
   - Success rate: 100.0%
   - Data integrity: ❌ ISSUE DETECTED

💰 Balance Analysis:
   - Expected deduction: 0.05 BTC
   - Actual deduction: 0.03 BTC
   - Difference: 0.02 BTC ⚠️ RACE CONDITION!
```

---

## 🔍 **RACE CONDITION INDICATORS**

### **🚨 Critical Issues:**
- **Balance mismatch**: Expected vs actual deduction differs
- **Negative balances**: Wallet balance goes below zero
- **Duplicate transactions**: Same withdrawal processed multiple times
- **Lost updates**: Some operations not reflected in database
- **Inconsistent state**: Related records don't match

### **⚠️ Warning Signs:**
- **High failure rate**: Too many concurrent requests failing
- **Timeout errors**: Database operations taking too long
- **Lock contention**: MongoDB reporting lock timeouts
- **Memory leaks**: Increasing memory usage during tests
- **Performance degradation**: Response times increasing

---

## 🛠️ **TROUBLESHOOTING**

### **Common Issues:**

#### **1. Connection Timeouts**
```bash
❌ Error: connect ECONNREFUSED 127.0.0.1:5000
```
**Solution:**
- Ensure backend is running: `docker-compose -f docker-compose.dev.yml up -d`
- Check port availability: `netstat -ano | findstr :5000`
- Verify environment configuration

#### **2. MongoDB Session Errors**
```bash
❌ Error: Transaction numbers are only allowed on a replica set member
```
**Solution:**
- Ensure MongoDB replica set is configured
- Check MongoDB connection string includes `replicaSet=rs0`
- Verify MongoDB is running in replica set mode

#### **3. Insufficient Test Data**
```bash
❌ Error: Insufficient balance for withdrawal
```
**Solution:**
- Run interest calculation first: `npm run calculate-earnings:local`
- Manually add test balance to wallet
- Check test user setup in script

#### **4. Authentication Errors**
```bash
❌ Error: Unauthorized - Invalid token
```
**Solution:**
- Update test token generation in script
- Verify user authentication system
- Check JWT secret configuration

### **Debug Commands:**
```bash
# Check backend logs
docker-compose -f docker-compose.dev.yml logs backend

# Check MongoDB status
docker-compose -f docker-compose.dev.yml exec mongodb mongosh --eval "rs.status()"

# Check wallet balances
mongosh "mongodb://localhost:27017/cryptoyield" --eval "db.wallets.find({}).pretty()"

# Check test user
mongosh "mongodb://localhost:27017/cryptoyield" --eval "db.users.find({email: /test/}).pretty()"
```

---

## 🎯 **BEST PRACTICES**

### **🔧 Before Running Tests:**
1. **Backup database** if testing on important data
2. **Ensure sufficient resources** (CPU, memory, connections)
3. **Run in isolated environment** to avoid affecting production
4. **Check system load** và network stability
5. **Verify all dependencies** are running correctly

### **📊 During Testing:**
1. **Monitor system resources** (CPU, memory, disk I/O)
2. **Watch database connections** và lock contention
3. **Check application logs** for errors và warnings
4. **Observe response times** và performance metrics
5. **Note any unusual behavior** or error patterns

### **🔍 After Testing:**
1. **Analyze test results** thoroughly
2. **Review error logs** for patterns
3. **Check data consistency** manually if needed
4. **Document any issues** found
5. **Plan fixes** for identified problems

---

## 🚀 **PRODUCTION DEPLOYMENT**

### **Pre-Production Testing:**
```bash
# Test in staging environment
./test-withdrawal-race-conditions.sh both docker

# Load testing with higher concurrency
# (Modify concurrentRequests in script to 20-50)

# Extended duration testing
# (Run tests multiple times over several hours)
```

### **Production Monitoring:**
- **Set up alerts** for withdrawal failures
- **Monitor database** lock contention
- **Track response times** for withdrawal endpoints
- **Log race condition** indicators
- **Implement circuit breakers** for high failure rates

---

## 📞 **SUPPORT**

### **Quick Reference:**
```bash
# Run all tests
test-withdrawal-race-conditions.bat both local

# Check test results
echo $?  # 0 = success, 1 = failure

# View detailed logs
docker-compose -f docker-compose.dev.yml logs backend | grep -i withdrawal

# Get help
test-withdrawal-race-conditions.bat help
```

**🎉 Your withdrawal system is now thoroughly tested for race conditions and data integrity issues!**
