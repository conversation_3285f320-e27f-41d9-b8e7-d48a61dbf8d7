@echo off
setlocal enabledelayedexpansion

REM 🧪 Test Calculate Earnings Script for Windows
REM Quick test script for calculate-existing-earnings in Docker

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "NC=[0m"

echo %CYAN%🧪 Testing Calculate Earnings Script in Docker%NC%
echo.

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Docker is not running. Please start Docker first.%NC%
    exit /b 1
)

REM Check if backend container is running
docker-compose -f docker-compose.dev.yml ps backend | findstr "Up" >nul
if errorlevel 1 (
    echo %YELLOW%⚠️ Backend container is not running. Starting it first...%NC%
    docker-compose -f docker-compose.dev.yml up -d backend
    if errorlevel 1 (
        echo %RED%❌ Failed to start backend container%NC%
        exit /b 1
    )
    
    echo %CYAN%⏳ Waiting for backend to be ready...%NC%
    timeout /t 15 /nobreak >nul
)

echo %GREEN%🚀 Running dry-run test in Docker...%NC%
echo.

REM Run dry-run test
docker-compose -f docker-compose.dev.yml exec backend npm run calculate-earnings:dry-run:docker
if errorlevel 1 (
    echo %RED%❌ Dry-run test failed%NC%
    exit /b 1
)

echo.
echo %GREEN%✅ Dry-run test completed!%NC%
echo.

set /p "confirm=Do you want to run the actual calculation? (y/N): "
if /i "%confirm%"=="y" (
    echo %YELLOW%🔄 Running actual calculation...%NC%
    echo.
    
    docker-compose -f docker-compose.dev.yml exec backend npm run calculate-earnings:docker
    if errorlevel 1 (
        echo %RED%❌ Calculation failed%NC%
        exit /b 1
    )
    
    echo.
    echo %GREEN%✅ Calculation completed!%NC%
    echo %CYAN%💡 Check the frontend withdrawal page to verify earnings%NC%
) else (
    echo %YELLOW%Operation cancelled.%NC%
)

endlocal
