@echo off
setlocal enabledelayedexpansion

REM 🐳 CryptoYield Backend Debug Helper Script for Windows
REM This script helps manage Docker debugging for the backend

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "PURPLE=[95m"
set "CYAN=[96m"
set "NC=[0m"

:print_header
echo.
echo %CYAN%🐳 ==================================%NC%
echo %CYAN%   CryptoYield Backend Debug Helper%NC%
echo %CYAN%🐳 ==================================%NC%
echo.
goto :eof

:show_usage
call :print_header
echo Usage: %~nx0 [COMMAND]
echo.
echo %YELLOW%Available Commands:%NC%
echo.
echo %GREEN%  start           🚀 Start development environment with hot reload%NC%
echo %GREEN%  debug           🐛 Start with debug inspector (port 9229)%NC%
echo %GREEN%  debug-break     🔍 Start with debug breakpoints (port 9230)%NC%
echo %GREEN%  stop            🛑 Stop all development containers%NC%
echo %GREEN%  restart         🔄 Restart backend container%NC%
echo %GREEN%  logs            📋 Show backend logs%NC%
echo %GREEN%  logs-follow     📋 Follow backend logs in real-time%NC%
echo %GREEN%  shell           💻 Open shell in backend container%NC%
echo %GREEN%  status          📊 Show container status%NC%
echo %GREEN%  clean           🧹 Clean up containers and volumes%NC%
echo %GREEN%  help            ❓ Show this help message%NC%
echo.
echo %YELLOW%Debug Ports:%NC%
echo %BLUE%  Backend API:     http://localhost:5000%NC%
echo %BLUE%  Debug Inspector: chrome://inspect (port 9229)%NC%
echo %BLUE%  Debug Break:     chrome://inspect (port 9230)%NC%
echo %BLUE%  MongoDB:         mongodb://localhost:27017%NC%
echo %BLUE%  Mongo Express:   http://localhost:8081%NC%
echo %BLUE%  Redis:           redis://localhost:6379%NC%
echo.
goto :eof

:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Docker is not running. Please start Docker first.%NC%
    exit /b 1
)
goto :eof

:start_dev
echo %GREEN%🚀 Starting development environment...%NC%
docker-compose -f docker-compose.dev.yml up -d
if errorlevel 1 (
    echo %RED%❌ Failed to start development environment%NC%
    exit /b 1
)
echo %GREEN%✅ Development environment started!%NC%
echo.
echo %YELLOW%🔗 Available Services:%NC%
echo %BLUE%  Backend API:     http://localhost:5000%NC%
echo %BLUE%  Debug Inspector: chrome://inspect (port 9229)%NC%
echo %BLUE%  MongoDB:         mongodb://localhost:27017%NC%
echo %BLUE%  Mongo Express:   http://localhost:8081 (admin/admin123)%NC%
echo %BLUE%  Redis:           redis://localhost:6379%NC%
echo.
echo %CYAN%💡 To debug: Open Chrome -^> chrome://inspect -^> Configure -^> Add localhost:9229%NC%
goto :eof

:start_debug_break
echo %PURPLE%🔍 Starting backend with debug breakpoints...%NC%
docker-compose -f docker-compose.dev.yml --profile debug up -d backend-debug
if errorlevel 1 (
    echo %RED%❌ Failed to start debug backend%NC%
    exit /b 1
)
echo %GREEN%✅ Backend debug started with breakpoints!%NC%
echo.
echo %YELLOW%🔗 Debug Services:%NC%
echo %BLUE%  Backend API:     http://localhost:5001%NC%
echo %BLUE%  Debug Inspector: chrome://inspect (port 9230)%NC%
echo.
echo %CYAN%💡 The backend will wait for debugger to attach before starting.%NC%
echo %CYAN%💡 Open Chrome -^> chrome://inspect -^> Configure -^> Add localhost:9230%NC%
goto :eof

:show_logs
echo %CYAN%📋 Showing backend logs...%NC%
docker-compose -f docker-compose.dev.yml logs backend
goto :eof

:follow_logs
echo %CYAN%📋 Following backend logs (Ctrl+C to stop)...%NC%
docker-compose -f docker-compose.dev.yml logs -f backend
goto :eof

:open_shell
echo %CYAN%💻 Opening shell in backend container...%NC%
docker-compose -f docker-compose.dev.yml exec backend /bin/bash
goto :eof

:show_status
echo %CYAN%📊 Container Status:%NC%
docker-compose -f docker-compose.dev.yml ps
echo.
echo %CYAN%📊 Resource Usage:%NC%
for /f "tokens=*" %%i in ('docker-compose -f docker-compose.dev.yml ps -q') do (
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" %%i 2>nul
)
goto :eof

:restart_backend
echo %YELLOW%🔄 Restarting backend container...%NC%
docker-compose -f docker-compose.dev.yml restart backend
if errorlevel 1 (
    echo %RED%❌ Failed to restart backend%NC%
    exit /b 1
)
echo %GREEN%✅ Backend restarted!%NC%
goto :eof

:stop_containers
echo %YELLOW%🛑 Stopping development containers...%NC%
docker-compose -f docker-compose.dev.yml down
if errorlevel 1 (
    echo %RED%❌ Failed to stop containers%NC%
    exit /b 1
)
echo %GREEN%✅ Containers stopped!%NC%
goto :eof

:clean_up
echo %YELLOW%🧹 Cleaning up containers and volumes...%NC%
docker-compose -f docker-compose.dev.yml down -v --remove-orphans
docker system prune -f
echo %GREEN%✅ Cleanup completed!%NC%
goto :eof

REM Main script logic
call :check_docker

set "command=%~1"
if "%command%"=="" set "command=help"

if "%command%"=="start" (
    call :start_dev
) else if "%command%"=="debug" (
    call :start_dev
    echo %PURPLE%🐛 Debug inspector available on port 9229%NC%
) else if "%command%"=="debug-break" (
    call :start_debug_break
) else if "%command%"=="stop" (
    call :stop_containers
) else if "%command%"=="restart" (
    call :restart_backend
) else if "%command%"=="logs" (
    call :show_logs
) else if "%command%"=="logs-follow" (
    call :follow_logs
) else if "%command%"=="shell" (
    call :open_shell
) else if "%command%"=="status" (
    call :show_status
) else if "%command%"=="clean" (
    call :clean_up
) else (
    call :show_usage
)

endlocal
