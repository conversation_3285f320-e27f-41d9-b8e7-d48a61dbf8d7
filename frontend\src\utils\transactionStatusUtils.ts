/**
 * Transaction Status Utilities
 * Centralized utilities for handling transaction status display and logic
 */

export type TransactionStatus = 'pending' | 'completed' | 'failed' | 'approved' | 'rejected';

export interface StatusDisplayConfig {
  color: string;
  colorScheme: string;
  label: string;
  description: string;
  icon: string;
  priority: number; // For sorting
}

/**
 * Get standardized status display configuration
 */
export const getTransactionStatusConfig = (status: TransactionStatus): StatusDisplayConfig => {
  const configs: Record<TransactionStatus, StatusDisplayConfig> = {
    pending: {
      color: '#F0B90B',
      colorScheme: 'yellow',
      label: 'Pending',
      description: 'Transaction is waiting for processing or approval',
      icon: 'clock',
      priority: 1
    },
    approved: {
      color: '#0ECB81',
      colorScheme: 'green',
      label: 'Approved',
      description: 'Transaction has been approved and is being processed',
      icon: 'check',
      priority: 2
    },
    completed: {
      color: '#0ECB81',
      colorScheme: 'green',
      label: 'Completed',
      description: 'Transaction has been successfully completed',
      icon: 'check-circle',
      priority: 3
    },
    rejected: {
      color: '#F84960',
      colorScheme: 'red',
      label: 'Rejected',
      description: 'Transaction has been rejected and will not be processed',
      icon: 'times',
      priority: 4
    },
    failed: {
      color: '#F84960',
      colorScheme: 'red',
      label: 'Failed',
      description: 'Transaction processing failed due to an error',
      icon: 'exclamation-triangle',
      priority: 5
    }
  };

  return configs[status] || {
    color: '#848E9C',
    colorScheme: 'gray',
    label: status.charAt(0).toUpperCase() + status.slice(1),
    description: 'Unknown transaction status',
    icon: 'question',
    priority: 99
  };
};

/**
 * Get status color for Chakra UI Badge component
 */
export const getStatusColorScheme = (status: string): string => {
  return getTransactionStatusConfig(status as TransactionStatus).colorScheme;
};

/**
 * Get status color hex value
 */
export const getStatusColor = (status: string): string => {
  return getTransactionStatusConfig(status as TransactionStatus).color;
};

/**
 * Get formatted status label
 */
export const getStatusLabel = (status: string): string => {
  return getTransactionStatusConfig(status as TransactionStatus).label;
};

/**
 * Get status description
 */
export const getStatusDescription = (status: string): string => {
  return getTransactionStatusConfig(status as TransactionStatus).description;
};

/**
 * Check if status indicates success
 */
export const isSuccessStatus = (status: string): boolean => {
  return ['completed', 'approved'].includes(status.toLowerCase());
};

/**
 * Check if status indicates failure
 */
export const isFailureStatus = (status: string): boolean => {
  return ['failed', 'rejected'].includes(status.toLowerCase());
};

/**
 * Check if status indicates pending state
 */
export const isPendingStatus = (status: string): boolean => {
  return status.toLowerCase() === 'pending';
};

/**
 * Check if status is final (cannot be changed)
 */
export const isFinalStatus = (status: string): boolean => {
  return ['completed', 'rejected', 'failed'].includes(status.toLowerCase());
};

/**
 * Get allowed status transitions
 */
export const getAllowedStatusTransitions = (currentStatus: string): TransactionStatus[] => {
  const transitions: Record<string, TransactionStatus[]> = {
    pending: ['approved', 'rejected', 'failed'],
    approved: ['completed', 'failed'],
    rejected: [], // Final state
    completed: [], // Final state
    failed: ['pending'] // Can retry
  };

  return transitions[currentStatus.toLowerCase()] || [];
};

/**
 * Check if status transition is allowed
 */
export const canTransitionStatus = (currentStatus: string, newStatus: string): boolean => {
  const allowedTransitions = getAllowedStatusTransitions(currentStatus);
  return allowedTransitions.includes(newStatus as TransactionStatus);
};

/**
 * Get status priority for sorting
 */
export const getStatusPriority = (status: string): number => {
  return getTransactionStatusConfig(status as TransactionStatus).priority;
};

/**
 * Sort transactions by status priority
 */
export const sortByStatusPriority = (transactions: any[]): any[] => {
  return [...transactions].sort((a, b) => {
    const priorityA = getStatusPriority(a.status);
    const priorityB = getStatusPriority(b.status);
    return priorityA - priorityB;
  });
};

/**
 * Get status-specific alert configuration
 */
export const getStatusAlert = (status: string) => {
  const config = getTransactionStatusConfig(status as TransactionStatus);
  
  switch (status.toLowerCase()) {
    case 'pending':
      return {
        status: 'warning' as const,
        title: 'Pending Approval',
        description: 'This transaction is waiting for admin approval.',
        bg: '#F0B90B22',
        color: '#F0B90B'
      };
    case 'approved':
      return {
        status: 'info' as const,
        title: 'Approved',
        description: 'Transaction has been approved and is being processed.',
        bg: '#0ECB8122',
        color: '#0ECB81'
      };
    case 'completed':
      return {
        status: 'success' as const,
        title: 'Completed',
        description: 'Transaction has been successfully completed.',
        bg: '#0ECB8122',
        color: '#0ECB81'
      };
    case 'rejected':
      return {
        status: 'error' as const,
        title: 'Rejected',
        description: 'Transaction has been rejected and will not be processed.',
        bg: '#F8496022',
        color: '#F84960'
      };
    case 'failed':
      return {
        status: 'error' as const,
        title: 'Failed',
        description: 'Transaction processing failed. Please contact support if this persists.',
        bg: '#F8496022',
        color: '#F84960'
      };
    default:
      return {
        status: 'info' as const,
        title: config.label,
        description: config.description,
        bg: '#848E9C22',
        color: '#848E9C'
      };
  }
};

/**
 * Format status for display with proper capitalization
 */
export const formatStatusDisplay = (status: string): string => {
  return getStatusLabel(status);
};

/**
 * Get status badge props for consistent styling
 */
export const getStatusBadgeProps = (status: string) => {
  const config = getTransactionStatusConfig(status as TransactionStatus);
  
  return {
    colorScheme: config.colorScheme,
    variant: 'subtle',
    fontSize: 'xs',
    fontWeight: 'medium',
    textTransform: 'uppercase' as const,
    borderRadius: 'md',
    px: 2,
    py: 1
  };
};

/**
 * Get transaction type specific status messages
 */
export const getTransactionTypeStatusMessage = (type: string, status: string): string => {
  const statusConfig = getTransactionStatusConfig(status as TransactionStatus);
  
  switch (type.toLowerCase()) {
    case 'withdrawal':
      switch (status.toLowerCase()) {
        case 'pending':
          return 'Your withdrawal request is being reviewed by our team.';
        case 'approved':
          return 'Your withdrawal has been approved and is being processed.';
        case 'completed':
          return 'Your withdrawal has been successfully sent to your wallet.';
        case 'rejected':
          return 'Your withdrawal request has been rejected. Please check the details or contact support.';
        case 'failed':
          return 'Your withdrawal failed to process. Please try again or contact support.';
        default:
          return statusConfig.description;
      }
    case 'deposit':
      switch (status.toLowerCase()) {
        case 'pending':
          return 'Your deposit is being confirmed on the blockchain.';
        case 'approved':
          return 'Your deposit has been confirmed and approved.';
        case 'completed':
          return 'Your deposit has been successfully credited to your account.';
        case 'rejected':
          return 'Your deposit has been rejected. Please contact support.';
        case 'failed':
          return 'Your deposit failed to process. Please contact support.';
        default:
          return statusConfig.description;
      }
    default:
      return statusConfig.description;
  }
};

/**
 * Export all status constants for easy access
 */
export const TRANSACTION_STATUSES = {
  PENDING: 'pending' as const,
  APPROVED: 'approved' as const,
  COMPLETED: 'completed' as const,
  REJECTED: 'rejected' as const,
  FAILED: 'failed' as const
} as const;

/**
 * Export status arrays for validation
 */
export const ALL_STATUSES: TransactionStatus[] = ['pending', 'approved', 'completed', 'rejected', 'failed'];
export const ACTIVE_STATUSES: TransactionStatus[] = ['pending', 'approved'];
export const FINAL_STATUSES: TransactionStatus[] = ['completed', 'rejected', 'failed'];
export const SUCCESS_STATUSES: TransactionStatus[] = ['completed', 'approved'];
export const FAILURE_STATUSES: TransactionStatus[] = ['rejected', 'failed'];
