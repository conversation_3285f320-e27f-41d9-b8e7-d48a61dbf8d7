@echo off
setlocal enabledelayedexpansion

REM 🧪 Withdrawal Race Condition Test Script for Windows
REM Tests concurrent withdrawals to identify potential race conditions and data integrity issues

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "PURPLE=[95m"
set "CYAN=[96m"
set "NC=[0m"

:print_header
echo.
echo %CYAN%🧪 ====================================%NC%
echo %CYAN%   Withdrawal Race Condition Tests%NC%
echo %CYAN%🧪 ====================================%NC%
echo.
goto :eof

:show_usage
call :print_header
echo Usage: %~nx0 [TEST_TYPE] [ENVIRONMENT]
echo.
echo %YELLOW%Available Test Types:%NC%
echo.
echo %GREEN%  concurrent      🚀 Test concurrent HTTP withdrawal requests%NC%
echo %GREEN%  database        🔄 Test database-level race conditions%NC%
echo %GREEN%  both            🎯 Run both test types%NC%
echo %GREEN%  help            ❓ Show this help message%NC%
echo.
echo %YELLOW%Available Environments:%NC%
echo.
echo %BLUE%  local           🏠 Run with local environment%NC%
echo %BLUE%  docker          🐳 Run in Docker container%NC%
echo.
echo %YELLOW%What these tests do:%NC%
echo %BLUE%  • Test concurrent withdrawal operations%NC%
echo %BLUE%  • Identify race conditions and data corruption%NC%
echo %BLUE%  • Verify transaction safety and data integrity%NC%
echo %BLUE%  • Check wallet balance consistency%NC%
echo %BLUE%  • Validate MongoDB session handling%NC%
echo.
echo %YELLOW%Examples:%NC%
echo %CYAN%  test-withdrawal-race-conditions.bat concurrent local%NC%
echo %CYAN%  test-withdrawal-race-conditions.bat database docker%NC%
echo %CYAN%  test-withdrawal-race-conditions.bat both local%NC%
echo.
goto :eof

:check_backend
if not exist "backend" (
    echo %RED%❌ Backend directory not found. Please run from project root.%NC%
    exit /b 1
)
goto :eof

:check_docker
if "%~2"=="docker" (
    docker info >nul 2>&1
    if errorlevel 1 (
        echo %RED%❌ Docker is not running. Please start Docker first.%NC%
        exit /b 1
    )
    
    docker-compose -f docker-compose.dev.yml ps backend | findstr "Up" >nul
    if errorlevel 1 (
        echo %YELLOW%⚠️ Backend container is not running. Starting it first...%NC%
        docker-compose -f docker-compose.dev.yml up -d backend
        if errorlevel 1 (
            echo %RED%❌ Failed to start backend container%NC%
            exit /b 1
        )
        
        echo %CYAN%⏳ Waiting for backend to be ready...%NC%
        timeout /t 10 /nobreak >nul
    )
)
goto :eof

:run_concurrent_test
set "env=%~1"
echo %PURPLE%🚀 Running Concurrent Withdrawal Test (%env%)...%NC%
echo.

if "%env%"=="local" (
    cd backend
    npm run test:concurrent-withdrawals:local
    set "result=%errorlevel%"
    cd ..
) else if "%env%"=="docker" (
    docker-compose -f docker-compose.dev.yml exec backend npm run test:concurrent-withdrawals:docker
    set "result=%errorlevel%"
) else (
    echo %RED%❌ Invalid environment: %env%%NC%
    exit /b 1
)

if !result! equ 0 (
    echo %GREEN%✅ Concurrent withdrawal test passed%NC%
) else (
    echo %RED%❌ Concurrent withdrawal test failed%NC%
)

exit /b !result!

:run_database_test
set "env=%~1"
echo %PURPLE%🔄 Running Database Race Condition Test (%env%)...%NC%
echo.

if "%env%"=="local" (
    cd backend
    npm run test:db-race-conditions:local
    set "result=%errorlevel%"
    cd ..
) else if "%env%"=="docker" (
    docker-compose -f docker-compose.dev.yml exec backend npm run test:db-race-conditions:docker
    set "result=%errorlevel%"
) else (
    echo %RED%❌ Invalid environment: %env%%NC%
    exit /b 1
)

if !result! equ 0 (
    echo %GREEN%✅ Database race condition test passed%NC%
) else (
    echo %RED%❌ Database race condition test failed%NC%
)

exit /b !result!

:show_pre_test_info
echo %YELLOW%📋 Before running these tests:%NC%
echo %BLUE%  • Make sure your backend is running%NC%
echo %BLUE%  • Ensure database is accessible%NC%
echo %BLUE%  • These tests will create temporary test data%NC%
echo %BLUE%  • Tests will clean up after completion%NC%
echo %BLUE%  • Tests may take several minutes to complete%NC%
echo.

set /p "confirm=Do you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo %YELLOW%Tests cancelled.%NC%
    exit /b 0
)
goto :eof

:show_post_test_info
set "concurrent_result=%~1"
set "database_result=%~2"

echo.
echo %GREEN%✅ Race condition tests completed!%NC%
echo.
echo %YELLOW%📊 Test Results:%NC%
if "%concurrent_result%"=="0" (
    echo %GREEN%  • Concurrent withdrawal test: PASSED%NC%
) else if "%concurrent_result%"=="1" (
    echo %RED%  • Concurrent withdrawal test: FAILED%NC%
) else (
    echo %BLUE%  • Concurrent withdrawal test: SKIPPED%NC%
)

if "%database_result%"=="0" (
    echo %GREEN%  • Database race condition test: PASSED%NC%
) else if "%database_result%"=="1" (
    echo %RED%  • Database race condition test: FAILED%NC%
) else (
    echo %BLUE%  • Database race condition test: SKIPPED%NC%
)

echo.
echo %CYAN%💡 Next steps:%NC%
echo %BLUE%  • Review test output for any warnings%NC%
echo %BLUE%  • Check logs for detailed error information%NC%
echo %BLUE%  • Fix any identified race conditions%NC%
echo %BLUE%  • Re-run tests to verify fixes%NC%
echo.
goto :eof

REM Main script logic
call :check_backend

set "test_type=%~1"
set "environment=%~2"

if "%test_type%"=="" set "test_type=help"
if "%environment%"=="" set "environment=local"

if "%test_type%"=="help" (
    call :show_usage
    exit /b 0
)

if not "%test_type%"=="concurrent" if not "%test_type%"=="database" if not "%test_type%"=="both" (
    echo %RED%❌ Invalid test type: %test_type%%NC%
    call :show_usage
    exit /b 1
)

if not "%environment%"=="local" if not "%environment%"=="docker" (
    echo %RED%❌ Invalid environment: %environment%%NC%
    call :show_usage
    exit /b 1
)

call :print_header
call :show_pre_test_info
call :check_docker "%test_type%" "%environment%"

set "concurrent_result=999"
set "database_result=999"

if "%test_type%"=="concurrent" (
    call :run_concurrent_test "%environment%"
    set "concurrent_result=%errorlevel%"
) else if "%test_type%"=="database" (
    call :run_database_test "%environment%"
    set "database_result=%errorlevel%"
) else if "%test_type%"=="both" (
    call :run_concurrent_test "%environment%"
    set "concurrent_result=%errorlevel%"
    
    echo.
    echo %CYAN%🔄 Moving to next test...%NC%
    echo.
    
    call :run_database_test "%environment%"
    set "database_result=%errorlevel%"
)

call :show_post_test_info "%concurrent_result%" "%database_result%"

REM Exit with error if any test failed
if "%concurrent_result%"=="1" exit /b 1
if "%database_result%"=="1" exit /b 1

exit /b 0

endlocal
