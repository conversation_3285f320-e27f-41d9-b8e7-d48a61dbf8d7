# 🐳 **DOCKER DEBUG SETUP GUIDE**

## 📋 **OVERVIEW**

Hướng dẫn thiết lập debug cho backend CryptoYield trong Docker với hot reload và Node.js inspector.

---

## 🚀 **QUICK START**

### **Windows:**
```bash
# Start development environment with debug
debug-docker.bat debug

# Start with breakpoints (waits for debugger)
debug-docker.bat debug-break

# Show help
debug-docker.bat help
```

### **Linux/Mac:**
```bash
# Make script executable
chmod +x debug-docker.sh

# Start development environment with debug
./debug-docker.sh debug

# Start with breakpoints (waits for debugger)
./debug-docker.sh debug-break

# Show help
./debug-docker.sh help
```

---

## 🔧 **AVAILABLE COMMANDS**

| Command | Description | Port |
|---------|-------------|------|
| `start` | 🚀 Start development environment with hot reload | 5000 |
| `debug` | 🐛 Start with debug inspector | 9229 |
| `debug-break` | 🔍 Start with debug breakpoints | 9230 |
| `stop` | 🛑 Stop all development containers | - |
| `restart` | 🔄 Restart backend container | - |
| `logs` | 📋 Show backend logs | - |
| `logs-follow` | 📋 Follow backend logs in real-time | - |
| `shell` | 💻 Open shell in backend container | - |
| `status` | 📊 Show container status | - |
| `clean` | 🧹 Clean up containers and volumes | - |

---

## 🔗 **SERVICE PORTS**

### **Development Services:**
- **Backend API**: http://localhost:5000
- **Debug Inspector**: chrome://inspect (port 9229)
- **Debug Breakpoints**: chrome://inspect (port 9230)
- **MongoDB**: mongodb://localhost:27017
- **Mongo Express**: http://localhost:8081 (admin/admin123)
- **Redis**: redis://localhost:6379

### **Debug Modes:**

#### **1. Normal Debug (Port 9229)**
```bash
# Start with inspector attached
debug-docker.bat debug
```
- Backend starts normally
- Debug inspector available immediately
- Can attach debugger anytime

#### **2. Debug with Breakpoints (Port 9230)**
```bash
# Start and wait for debugger
debug-docker.bat debug-break
```
- Backend waits for debugger to attach
- Stops at first line of code
- Perfect for debugging startup issues

---

## 🛠️ **CHROME DEBUGGER SETUP**

### **Step 1: Open Chrome DevTools**
1. Open Chrome browser
2. Navigate to: `chrome://inspect`
3. Click "Configure" button

### **Step 2: Add Debug Targets**
Add these targets:
- `localhost:9229` (normal debug)
- `localhost:9230` (debug with breakpoints)

### **Step 3: Connect to Debugger**
1. Start backend with debug: `debug-docker.bat debug`
2. In Chrome DevTools, click "inspect" under Remote Target
3. Set breakpoints in Sources tab
4. Debug your code!

---

## 📁 **FILE STRUCTURE**

### **Debug Configuration Files:**
```
├── docker-compose.dev.yml     # Development compose with debug
├── backend/
│   ├── Dockerfile.dev         # Development Dockerfile
│   ├── nodemon.json          # Nodemon configuration
│   └── package.json          # Scripts for debug modes
├── debug-docker.sh           # Linux/Mac debug helper
├── debug-docker.bat          # Windows debug helper
└── Docker_Debug_Setup.md     # This documentation
```

### **Key Debug Scripts in package.json:**
```json
{
  "scripts": {
    "dev:docker": "NODE_ENV=development nodemon",
    "debug:docker": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true dotenv -e .env.docker -- node --inspect -r ts-node/register src/index.ts"
  }
}
```

---

## 🔄 **HOT RELOAD CONFIGURATION**

### **Volume Mounts for Hot Reload:**
```yaml
volumes:
  # Mount source code for hot reload
  - ./backend:/app:cached
  # Use named volume for node_modules to avoid conflicts
  - backend_node_modules:/app/node_modules
  # Mount uploads directory separately
  - ./backend/uploads:/app/uploads:cached
```

### **Nodemon Configuration:**
```json
{
  "watch": ["src"],
  "ext": "ts,js,json",
  "ignore": ["src/**/*.spec.ts", "src/**/*.test.ts"],
  "exec": "ts-node --transpile-only src/index.ts",
  "delay": 1000,
  "verbose": true,
  "legacyWatch": true,
  "polling": true
}
```

---

## 🐛 **DEBUGGING FEATURES**

### **Environment Variables for Debug:**
```yaml
environment:
  - NODE_ENV=development
  - DEBUG=*                              # Enable all debug logs
  - NODE_OPTIONS=--inspect=0.0.0.0:9229  # Debug inspector
  - TS_NODE_TRANSPILE_ONLY=true         # Faster TypeScript compilation
  - LOG_LEVEL=debug                      # Detailed logging
```

### **Debug Capabilities:**
- ✅ **Breakpoints**: Set breakpoints in TypeScript source
- ✅ **Step Debugging**: Step through code line by line
- ✅ **Variable Inspection**: Inspect variables and scope
- ✅ **Console Evaluation**: Execute code in debug context
- ✅ **Call Stack**: View complete call stack
- ✅ **Hot Reload**: Code changes trigger automatic restart
- ✅ **Source Maps**: Debug TypeScript directly

---

## 📊 **MONITORING & LOGS**

### **View Logs:**
```bash
# Show recent logs
debug-docker.bat logs

# Follow logs in real-time
debug-docker.bat logs-follow

# Check container status
debug-docker.bat status
```

### **Log Levels Available:**
- `error`: Error messages only
- `warn`: Warnings and errors
- `info`: General information
- `debug`: Detailed debug information
- `*`: All debug namespaces

---

## 🔧 **TROUBLESHOOTING**

### **Common Issues:**

#### **1. Port Already in Use**
```bash
# Check what's using the port
netstat -ano | findstr :5000
netstat -ano | findstr :9229

# Kill process if needed
taskkill /PID <PID> /F
```

#### **2. Docker Not Running**
```bash
# Check Docker status
docker info

# Start Docker Desktop
# Windows: Start Docker Desktop application
# Linux: sudo systemctl start docker
```

#### **3. Hot Reload Not Working**
```bash
# Restart backend container
debug-docker.bat restart

# Check volume mounts
docker-compose -f docker-compose.dev.yml exec backend ls -la /app/src
```

#### **4. Debug Inspector Not Connecting**
1. Verify port 9229/9230 is exposed
2. Check Chrome DevTools configuration
3. Ensure `--inspect` flag is set correctly
4. Try refreshing Chrome DevTools

### **Debug Commands:**
```bash
# Open shell in container
debug-docker.bat shell

# Check environment variables
docker-compose -f docker-compose.dev.yml exec backend env | grep NODE

# Check process list
docker-compose -f docker-compose.dev.yml exec backend ps aux
```

---

## 🎯 **BEST PRACTICES**

### **Development Workflow:**
1. **Start Environment**: `debug-docker.bat debug`
2. **Set Breakpoints**: In Chrome DevTools Sources tab
3. **Make Changes**: Edit TypeScript files
4. **Auto Restart**: Nodemon detects changes and restarts
5. **Debug**: Breakpoints hit automatically

### **Performance Tips:**
- Use `TS_NODE_TRANSPILE_ONLY=true` for faster compilation
- Mount only necessary directories
- Use named volumes for node_modules
- Enable polling for file watching in Docker

### **Security Notes:**
- Debug ports only exposed in development
- No authentication required for local debugging
- MongoDB runs without auth in development
- Use strong passwords in production

---

## 🚀 **PRODUCTION DEPLOYMENT**

### **Switch to Production:**
```bash
# Stop development environment
debug-docker.bat stop

# Start production environment
docker-compose up -d
```

### **Production vs Development:**
| Feature | Development | Production |
|---------|-------------|------------|
| Debug Ports | ✅ Exposed | ❌ Not exposed |
| Hot Reload | ✅ Enabled | ❌ Disabled |
| Source Maps | ✅ Enabled | ❌ Disabled |
| Logging | 🔍 Debug level | ℹ️ Info level |
| MongoDB Auth | ❌ Disabled | ✅ Enabled |

---

## 📞 **SUPPORT**

### **Quick Reference:**
```bash
# Start debugging
debug-docker.bat debug

# Open Chrome DevTools
chrome://inspect

# View logs
debug-docker.bat logs-follow

# Get help
debug-docker.bat help
```

**🎉 Happy Debugging! Your backend is now ready for professional development with full debug capabilities.**
