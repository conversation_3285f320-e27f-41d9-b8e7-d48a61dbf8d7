import express from 'express';
import {
  getMigrationStatus,
  migrateUserAddresses,
  comparePerformance,
  getUserAddresses,
  testNormalizedOperations
} from '../controllers/walletMigrationController';
import { protect } from '../middleware/authMiddleware';

const router = express.Router();

/**
 * @route   GET /api/wallet-migration/status
 * @desc    Get overall migration status and metrics
 * @access  Private
 */
router.get('/status', protect, getMigrationStatus);

/**
 * @route   POST /api/wallet-migration/migrate-user
 * @desc    Migrate specific user's addresses to normalized format
 * @access  Private
 */
router.post('/migrate-user', protect, migrateUserAddresses);

/**
 * @route   GET /api/wallet-migration/compare-performance
 * @desc    Compare performance between embedded and normalized approaches
 * @access  Private
 */
router.get('/compare-performance', protect, comparePerformance);

/**
 * @route   GET /api/wallet-migration/addresses
 * @desc    Get user's addresses with source information
 * @access  Private
 */
router.get('/addresses', protect, getUserAddresses);

/**
 * @route   POST /api/wallet-migration/test
 * @desc    Test normalized wallet operations
 * @access  Private
 */
router.post('/test', protect, testNormalizedOperations);

export default router;
