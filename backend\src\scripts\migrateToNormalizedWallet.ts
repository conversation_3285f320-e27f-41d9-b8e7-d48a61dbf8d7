#!/usr/bin/env node

/**
 * Migration Script: Embedded Addresses to Normalized WalletDetail
 * 
 * This script migrates embedded addresses from Wallet.assets.addresses
 * to the normalized WalletDetail collection for better performance.
 * 
 * Usage:
 *   npm run migrate:normalize-wallet
 *   or
 *   node dist/scripts/migrateToNormalizedWallet.js
 * 
 * Options:
 *   --dry-run: Preview migration without making changes
 *   --batch-size: Number of wallets to process per batch (default: 100)
 *   --validate: Validate migration results
 */

import mongoose from 'mongoose';
import Wallet from '../models/walletModel';
import WalletDetail from '../models/walletDetailModel';
import { logger } from '../utils/logger';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface MigrationStats {
  totalWallets: number;
  processedWallets: number;
  totalAddresses: number;
  migratedAddresses: number;
  skippedAddresses: number;
  errors: string[];
  startTime: Date;
  endTime?: Date;
  duration?: number;
}

interface MigrationOptions {
  dryRun: boolean;
  batchSize: number;
  validate: boolean;
}

class WalletNormalizationMigration {
  private stats: MigrationStats = {
    totalWallets: 0,
    processedWallets: 0,
    totalAddresses: 0,
    migratedAddresses: 0,
    skippedAddresses: 0,
    errors: [],
    startTime: new Date()
  };

  private options: MigrationOptions;

  constructor(options: MigrationOptions) {
    this.options = options;
  }

  async connectDatabase(): Promise<void> {
    try {
      const mongoUri = process.env.MONGO_URI || '*******************************************************************';
      await mongoose.connect(mongoUri);
      logger.info('✅ Connected to MongoDB for normalization migration');
    } catch (error) {
      logger.error('❌ Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnectDatabase(): Promise<void> {
    await mongoose.disconnect();
    logger.info('📡 Disconnected from MongoDB');
  }

  async runMigration(): Promise<MigrationStats> {
    try {
      logger.info('🚀 Starting Wallet Normalization Migration...');
      
      if (this.options.dryRun) {
        logger.info('🔍 DRY RUN MODE - No changes will be made');
      }

      // Get total counts
      this.stats.totalWallets = await Wallet.countDocuments({});
      logger.info(`📊 Found ${this.stats.totalWallets} wallets to process`);

      // Process in batches
      let skip = 0;
      const batchSize = this.options.batchSize;

      while (skip < this.stats.totalWallets) {
        const wallets = await Wallet.find({})
          .skip(skip)
          .limit(batchSize)
          .lean();

        logger.info(`📦 Processing batch ${Math.floor(skip / batchSize) + 1}: ${wallets.length} wallets`);

        for (const wallet of wallets) {
          await this.migrateWallet(wallet);
          this.stats.processedWallets++;
        }

        skip += batchSize;

        // Progress update
        const progress = Math.round((this.stats.processedWallets / this.stats.totalWallets) * 100);
        logger.info(`📈 Progress: ${progress}% (${this.stats.processedWallets}/${this.stats.totalWallets})`);
      }

      this.stats.endTime = new Date();
      this.stats.duration = this.stats.endTime.getTime() - this.stats.startTime.getTime();

      logger.info('✅ Migration completed successfully!');
      this.printStats();

      if (this.options.validate) {
        await this.validateMigration();
      }

      return this.stats;
    } catch (error) {
      logger.error('❌ Migration failed:', error);
      throw error;
    }
  }

  private async migrateWallet(wallet: any): Promise<void> {
    try {
      if (!wallet.assets || wallet.assets.length === 0) {
        return;
      }

      for (const asset of wallet.assets) {
        if (!asset.addresses || asset.addresses.length === 0) {
          continue;
        }

        for (const address of asset.addresses) {
          this.stats.totalAddresses++;

          try {
            // Check if already exists in WalletDetail
            const existing = await WalletDetail.findOne({
              address: address.address
            });

            if (existing) {
              this.stats.skippedAddresses++;
              logger.debug(`⏭️ Address ${address.address} already exists in WalletDetail`);
              continue;
            }

            if (!this.options.dryRun) {
              // Create in WalletDetail
              await WalletDetail.create({
                walletId: wallet._id,
                userId: wallet.userId,
                symbol: asset.symbol,
                address: address.address,
                network: address.network || 'mainnet',
                isDefault: address.isDefault || false,
                isActive: address.isActive !== false,
                privateKey: address.privateKey,
                label: address.label,
                addressIndex: address.addressIndex || 0,
                lastUpdated: address.lastUpdated || new Date(),
                withdrawalEnabled: address.withdrawalEnabled !== false,
                transactionCount: 0
              });
            }

            this.stats.migratedAddresses++;
            logger.debug(`✅ Migrated address ${address.address} for ${asset.symbol}`);

          } catch (error: any) {
            const errorMsg = `Failed to migrate address ${address.address}: ${error.message}`;
            this.stats.errors.push(errorMsg);
            logger.error(`❌ ${errorMsg}`);
          }
        }
      }
    } catch (error: any) {
      const errorMsg = `Failed to migrate wallet ${wallet._id}: ${error.message}`;
      this.stats.errors.push(errorMsg);
      logger.error(`❌ ${errorMsg}`);
    }
  }

  private async validateMigration(): Promise<void> {
    logger.info('🔍 Validating migration results...');

    try {
      // Count addresses in both collections
      const embeddedCount = await Wallet.aggregate([
        { $unwind: '$assets' },
        { $unwind: '$assets.addresses' },
        { $match: { 'assets.addresses.isActive': { $ne: false } } },
        { $count: 'total' }
      ]);

      const normalizedCount = await WalletDetail.countDocuments({ isActive: true });
      const embeddedTotal = embeddedCount[0]?.total || 0;

      logger.info(`📊 Validation Results:`);
      logger.info(`   - Embedded addresses: ${embeddedTotal}`);
      logger.info(`   - Normalized addresses: ${normalizedCount}`);
      logger.info(`   - Migration coverage: ${Math.round((normalizedCount / embeddedTotal) * 100)}%`);

      // Sample validation - check random addresses
      const sampleSize = Math.min(10, embeddedTotal);
      if (sampleSize > 0) {
        const sampleAddresses = await Wallet.aggregate([
          { $unwind: '$assets' },
          { $unwind: '$assets.addresses' },
          { $sample: { size: sampleSize } },
          { $project: { address: '$assets.addresses.address' } }
        ]);

        let validatedCount = 0;
        for (const sample of sampleAddresses) {
          const exists = await WalletDetail.findOne({ address: sample.address });
          if (exists) validatedCount++;
        }

        logger.info(`   - Sample validation: ${validatedCount}/${sampleSize} addresses found`);
      }

    } catch (error) {
      logger.error('❌ Validation failed:', error);
    }
  }

  private printStats(): void {
    logger.info('📊 Migration Statistics:');
    logger.info('========================');
    logger.info(`Total Wallets: ${this.stats.totalWallets}`);
    logger.info(`Processed Wallets: ${this.stats.processedWallets}`);
    logger.info(`Total Addresses: ${this.stats.totalAddresses}`);
    logger.info(`Migrated Addresses: ${this.stats.migratedAddresses}`);
    logger.info(`Skipped Addresses: ${this.stats.skippedAddresses}`);
    logger.info(`Errors: ${this.stats.errors.length}`);
    
    if (this.stats.duration) {
      logger.info(`Duration: ${Math.round(this.stats.duration / 1000)}s`);
      logger.info(`Speed: ${Math.round(this.stats.migratedAddresses / (this.stats.duration / 1000))} addresses/sec`);
    }

    if (this.stats.errors.length > 0) {
      logger.info('\n❌ Errors:');
      this.stats.errors.slice(0, 10).forEach(error => logger.info(`   - ${error}`));
      if (this.stats.errors.length > 10) {
        logger.info(`   ... and ${this.stats.errors.length - 10} more errors`);
      }
    }
  }
}

// Parse command line arguments
function parseArguments(): MigrationOptions {
  const args = process.argv.slice(2);
  
  return {
    dryRun: args.includes('--dry-run'),
    batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1] || '100'),
    validate: args.includes('--validate')
  };
}

// Main execution
async function main() {
  const options = parseArguments();
  const migration = new WalletNormalizationMigration(options);

  try {
    await migration.connectDatabase();
    await migration.runMigration();
  } catch (error) {
    logger.error('❌ Migration script failed:', error);
    process.exit(1);
  } finally {
    await migration.disconnectDatabase();
  }
}

// Run migration if called directly
if (require.main === module) {
  main().catch(error => {
    logger.error('❌ Migration script failed:', error);
    process.exit(1);
  });
}

export default WalletNormalizationMigration;
