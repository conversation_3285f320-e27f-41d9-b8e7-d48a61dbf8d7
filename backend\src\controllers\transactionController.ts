import { Request, Response } from 'express';
import Transaction from '../models/transactionModel';
import Wallet from '../models/walletModel';
import User from '../models/userModel';
import Investment from '../models/investmentModel';
import InvestmentPackage from '../models/investmentPackageModel';
import { ethers } from 'ethers';
import { FirstDepositCommissionService } from '../services/firstDepositCommissionService';
import { cacheService } from '../services/cacheService';
import { clearWalletCache } from '../utils/cacheUtils';
import { logger } from '../utils/logger';
import { getUserTransactions, updateTransaction, createTransaction } from '../utils/transactionUtils';
import { notificationService } from '../services/notificationService';

// Blockchain transaction validation
const validateBlockchainTx = async (txHash: string, blockchainNetwork?: string): Promise<boolean> => {
  try {
    // Default to Ethereum mainnet if no network is specified
    let providerUrl = process.env.PROVIDER_URL || 'https://eth-mainnet.g.alchemy.com/v2/demo';

    // Use different provider URLs based on the blockchain network
    if (blockchainNetwork === 'bsc') {
      providerUrl = process.env.BSC_PROVIDER_URL || 'https://bsc-dataseed.binance.org/';
    } else if (blockchainNetwork === 'tron') {
      // For TRON, we would need a different approach as ethers.js doesn't support TRON
      // This is just a placeholder
      logger.info(`TRON validation not implemented, txHash: ${txHash}`);
      return true; // Return true for now to avoid blocking the flow
    }

    // Ethers v6 için JsonRpcProvider kullanımı
    const provider = new ethers.JsonRpcProvider(providerUrl);
    const tx = await provider.getTransaction(txHash);
    return tx !== null && tx.blockNumber !== null;
  } catch (error) {
    logger.error('Blockchain validation error:', error);
    return false;
  }
};

// @desc    Get all transactions for a user
// @route   GET /api/transactions
// @access  Private
export const getTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : null;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : null;
    const type = req.query.type as string;
    const asset = req.query.asset as string;
    const status = req.query.status as string;

    const options: any = {
      page,
      limit,
      type,
      asset,
      status
    };

    // Apply date filters if provided
    if (startDate && endDate) {
      options.dateRange = { start: startDate, end: endDate };
    }

    const result = await getUserTransactions(req.user._id, options);

    res.json(result);

  } catch (error: any) {
    logger.error('Transaction fetch error:', error);
    res.status(500).json({
      message: 'İşlem geçmişi alınırken bir hata oluştu',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get transaction by ID
// @route   GET /api/transactions/:id
// @access  Private
export const getTransactionById = async (req: Request, res: Response): Promise<void> => {
  try {
    // Kiểm tra xem người dùng có phải là admin không
    const isAdmin = req.user.isAdmin === true;

    // Tạo query dựa trên quyền của người dùng
    const query: any = { _id: req.params.id };

    // Nếu không phải admin, chỉ cho phép xem giao dịch của chính mình
    if (!isAdmin) {
      query.userId = req.user._id;
    }

    // Tìm giao dịch với thông tin đầy đủ
    const transaction = await Transaction.findOne(query)
      .populate({
        path: 'userId',
        select: 'firstName lastName email phoneNumber country city createdAt',
        model: 'User'
      })
      .populate({
        path: 'investmentId',
        select: 'amount currency receiptUrl cryptoAddress network adminNotes',
        model: 'Investment'
      });

    if (!transaction) {
      res.status(404).json({ message: 'Không tìm thấy giao dịch' });
      return;
    }

    // Lấy thông tin ví của người dùng
    const wallet = await Wallet.findOne({ userId: transaction.userId });

    // Tính toán thông tin bổ sung
    const btcAmount = transaction.amount * 0.00001; // Giả định tỷ lệ quy đổi
    const commissionRate = 0.05; // 5% hoa hồng
    const commissionAmount = transaction.amount * commissionRate;

    // Lấy lịch sử giao dịch của người dùng
    const userTransactions = await Transaction.find({
      userId: transaction.userId,
      _id: { $ne: transaction._id } // Loại trừ giao dịch hiện tại
    })
    .sort({ createdAt: -1 })
    .limit(5);

    // Tính tổng khối lượng giao dịch
    const totalVolume = userTransactions.reduce((sum, tx) => sum + tx.amount, 0);

    // For blockchain transactions, get additional details
    let blockchainVerified = false;
    if (transaction.txHash) {
      blockchainVerified = await validateBlockchainTx(transaction.txHash, transaction.blockchainNetwork as string);
      transaction.set('blockchainVerified', blockchainVerified, { strict: false });
    }

    // Tạo đối tượng phản hồi với thông tin đầy đủ
    const response = {
      ...transaction.toObject(),
      btcAmount,
      commissionRate,
      commissionAmount,
      userTransactionHistory: {
        count: userTransactions.length,
        totalVolume
      },
      walletInfo: wallet ? {
        assets: wallet.assets
      } : null,
      blockchainVerified,
      receiptUrl: transaction.investmentId && typeof transaction.investmentId === 'object' ?
        (transaction.investmentId as any).receiptUrl || null : null
    };

    res.json(response);

  } catch (error: any) {
    logger.error('Transaction detail error:', error);
    res.status(500).json({
      message: 'Đã xảy ra lỗi khi lấy thông tin giao dịch',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Calculate commission for a deposit
// @route   POST /api/transactions/calculate-commission
// @access  Private
export const calculateCommission = async (req: Request, res: Response): Promise<void> => {
  try {
    const { asset, amount } = req.body;

    if (!asset || !amount || amount <= 0) {
      res.status(400).json({
        message: 'Geçersiz giriş',
        errors: {
          asset: !asset ? 'Kripto para birimi gerekli' : undefined,
          amount: !amount ? 'Miktar gerekli' : amount <= 0 ? 'Miktar 0\'dan büyük olmalı' : undefined
        }
      });
      return;
    }

    const commissionRate = process.env.COMMISSION_RATE ?
      parseFloat(process.env.COMMISSION_RATE) : 0.01;

    const commissionAmount = amount * commissionRate;

    res.json({
      asset,
      depositAmount: amount,
      commissionRate,
      commissionAmount,
      netAmount: amount - commissionAmount
    });

  } catch (error: any) {
    logger.error('Commission calculation error:', error);
    res.status(500).json({
      message: 'Komisyon hesaplanırken bir hata oluştu',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Calculate interest for an asset
// @route   POST /api/transactions/calculate-interest
// @access  Private
export const calculateInterest = async (req: Request, res: Response): Promise<void> => {
  try {
    const { asset, amount, days } = req.body;

    if (!asset || !amount || amount <= 0 || !days || days <= 0) {
      res.status(400).json({
        message: 'Geçersiz giriş',
        errors: {
          asset: !asset ? 'Kripto para birimi gerekli' : undefined,
          amount: !amount ? 'Miktar gerekli' : amount <= 0 ? 'Miktar 0\'dan büyük olmalı' : undefined,
          days: !days ? 'Gün sayısı gerekli' : days <= 0 ? 'Gün sayısı 0\'dan büyük olmalı' : undefined
        }
      });
      return;
    }

    const annualInterestRate = process.env.INTEREST_RATE_DEFAULT ?
      parseFloat(process.env.INTEREST_RATE_DEFAULT) : 0.05;

    const dailyInterestRate = annualInterestRate / 365;
    const interestAmount = amount * dailyInterestRate * days;
    const platformFee = interestAmount * 0.2; // 20% platform fee
    const netInterest = interestAmount - platformFee;

    res.json({
      asset,
      principalAmount: amount,
      annualInterestRate,
      dailyInterestRate,
      days,
      grossInterestAmount: interestAmount,
      platformFee,
      netInterestAmount: netInterest,
      totalReturn: amount + netInterest
    });

  } catch (error: any) {
    logger.error('Interest calculation error:', error);
    res.status(500).json({
      message: 'Faiz hesaplanırken bir hata oluştu',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get all transactions (admin only)
// @route   GET /api/transactions/admin
// @access  Admin
export const getAdminTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const type = req.query.type as string;
    const asset = req.query.asset as string;
    const status = req.query.status as string;
    const userId = req.query.userId as string;

    // Build query
    const query: any = {};
    if (type) query.type = type;
    if (asset) query.asset = asset;
    if (status) query.status = status;
    if (userId) query.userId = userId;

    // Execute query with pagination
    const transactions = await Transaction.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('userId', 'email firstName lastName');

    // Get total count
    const total = await Transaction.countDocuments(query);

    res.json({
      transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    logger.error('Admin transactions fetch error:', error);
    res.status(500).json({
      message: 'Failed to fetch transactions',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Update transaction status (admin only) - DISABLED
// @route   PUT /api/transactions/:id/status - DISABLED
// @access  Admin - DISABLED
// Note: This function has been disabled as per requirements to remove approve/reject functionality
export const updateTransactionStatus = async (req: Request, res: Response): Promise<void> => {
  // Return 404 to indicate this functionality is no longer available
  res.status(404).json({
    message: 'Transaction status update functionality has been disabled',
    error: 'This endpoint is no longer available'
  });
  return;

  // Note: All transaction status update logic has been removed as per requirements
};

// @desc    Get transaction summary for dashboard
// @route   GET /api/transactions/summary
// @access  Private
export const getTransactionSummary = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;

    // Get all transactions for the user
    const transactions = await Transaction.find({ userId }).sort({ createdAt: -1 });

    // Calculate summary statistics
    const totalTransactions = transactions.length;
    const totalDeposits = transactions
      .filter(tx => tx.type === 'deposit' && tx.status === 'completed')
      .reduce((sum, tx) => sum + tx.amount, 0);

    const totalWithdrawals = transactions
      .filter(tx => tx.type === 'withdrawal' && tx.status === 'completed')
      .reduce((sum, tx) => sum + tx.amount, 0);

    const pendingWithdrawals = transactions
      .filter(tx => tx.type === 'withdrawal' && tx.status === 'pending')
      .reduce((sum, tx) => sum + tx.amount, 0);

    // Calculate monthly statistics
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const monthlyTransactions = transactions.filter(tx =>
      new Date(tx.createdAt) >= currentMonth
    );

    const monthlyDeposits = monthlyTransactions
      .filter(tx => tx.type === 'deposit' && tx.status === 'completed')
      .reduce((sum, tx) => sum + tx.amount, 0);

    res.json({
      status: 'success',
      data: {
        totalTransactions,
        totalDeposits,
        totalWithdrawals,
        pendingWithdrawals,
        monthlyDeposits,
        recentTransactions: transactions.slice(0, 5)
      }
    });

  } catch (error: any) {
    logger.error('Transaction summary error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch transaction summary',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get withdrawable balance for specific cryptocurrency
// @route   GET /api/transactions/wallet/withdrawable/:crypto
// @access  Private
export const getWithdrawableBalance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { crypto } = req.params;
    const { type } = req.query; // balance, interest, commission
    const userId = req.user._id;

    if (!crypto) {
      res.status(400).json({
        status: 'error',
        message: 'Cryptocurrency parameter is required'
      });
      return;
    }

    // Get user's wallet
    const wallet = await Wallet.findOne({ userId });
    if (!wallet) {
      res.json({
        status: 'success',
        data: {
          withdrawableBalance: {
            asset: crypto.toUpperCase(),
            principalAmount: 0,
            interestAmount: 0,
            commissionAmount: 0,
            totalWithdrawable: 0,
            principalLocked: false,
            principalLockUntil: null,
            daysUntilUnlock: 0,
            meetsMinimumThreshold: false,
            minimumThreshold: 10, // Default minimum threshold
            lastCalculated: new Date().toISOString()
          }
        }
      });
      return;
    }

    // Find the specific asset
    const asset = wallet.assets.find(a => a.symbol === crypto.toUpperCase());
    if (!asset) {
      res.json({
        status: 'success',
        data: {
          withdrawableBalance: {
            asset: crypto.toUpperCase(),
            principalAmount: 0,
            interestAmount: 0,
            commissionAmount: 0,
            totalWithdrawable: 0,
            principalLocked: false,
            principalLockUntil: null,
            daysUntilUnlock: 0,
            meetsMinimumThreshold: false,
            minimumThreshold: 10,
            lastCalculated: new Date().toISOString()
          }
        }
      });
      return;
    }

    // Get investment packages to check lock status
    const packages = await InvestmentPackage.find({
      userId,
      currency: crypto.toUpperCase(),
      status: 'active'
    }).sort({ activatedAt: 1 }); // Oldest first

    // Calculate lock status based on 30-day rule
    let principalLocked = false;
    let principalLockUntil: string | null = null;
    let daysUntilUnlock = 0;

    if (packages.length > 0) {
      const oldestPackage = packages[0];
      const activatedDate = new Date(oldestPackage.activatedAt);
      const lockExpiryDate = new Date(activatedDate.getTime() + (30 * 24 * 60 * 60 * 1000)); // 30 days
      const now = new Date();

      if (now < lockExpiryDate) {
        principalLocked = true;
        principalLockUntil = lockExpiryDate.toISOString();
        daysUntilUnlock = Math.ceil((lockExpiryDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000));
      }
    }

    // Calculate amounts
    const principalAmount = principalLocked ? 0 : (asset.balance || 0);
    const interestAmount = asset.interestBalance || 0;
    const commissionAmount = asset.commissionBalance || 0;

    // If type is specified, return only that type
    let totalWithdrawable = 0;
    if (type) {
      switch (type) {
        case 'balance':
          totalWithdrawable = principalAmount;
          break;
        case 'interest':
          totalWithdrawable = interestAmount;
          break;
        case 'commission':
          totalWithdrawable = commissionAmount;
          break;
        default:
          totalWithdrawable = principalAmount + interestAmount + commissionAmount;
      }
    } else {
      totalWithdrawable = principalAmount + interestAmount + commissionAmount;
    }

    // Check minimum threshold
    const minimumThreshold = 10; // Can be configurable per asset
    const meetsMinimumThreshold = totalWithdrawable >= minimumThreshold;

    res.json({
      status: 'success',
      data: {
        withdrawableBalance: {
          asset: crypto.toUpperCase(),
          principalAmount,
          interestAmount,
          commissionAmount,
          totalWithdrawable,
          principalLocked,
          principalLockUntil,
          daysUntilUnlock,
          meetsMinimumThreshold,
          minimumThreshold,
          lastCalculated: new Date().toISOString()
        }
      }
    });

  } catch (error: any) {
    logger.error('Get withdrawable balance error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get withdrawable balance',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};


