#!/bin/bash

# 🧪 Withdrawal Race Condition Test Script
# Tests concurrent withdrawals to identify potential race conditions and data integrity issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

# Function to print header
print_header() {
    echo ""
    print_color $CYAN "🧪 ===================================="
    print_color $CYAN "   Withdrawal Race Condition Tests"
    print_color $CYAN "🧪 ===================================="
    echo ""
}

# Function to show usage
show_usage() {
    print_header
    echo "Usage: $0 [TEST_TYPE] [ENVIRONMENT]"
    echo ""
    print_color $YELLOW "Available Test Types:"
    echo ""
    print_color $GREEN "  concurrent      🚀 Test concurrent HTTP withdrawal requests"
    print_color $GREEN "  database        🔄 Test database-level race conditions"
    print_color $GREEN "  both            🎯 Run both test types"
    print_color $GREEN "  help            ❓ Show this help message"
    echo ""
    print_color $YELLOW "Available Environments:"
    echo ""
    print_color $BLUE "  local           🏠 Run with local environment"
    print_color $BLUE "  docker          🐳 Run in Docker container"
    echo ""
    print_color $YELLOW "What these tests do:"
    print_color $BLUE "  • Test concurrent withdrawal operations"
    print_color $BLUE "  • Identify race conditions and data corruption"
    print_color $BLUE "  • Verify transaction safety and data integrity"
    print_color $BLUE "  • Check wallet balance consistency"
    print_color $BLUE "  • Validate MongoDB session handling"
    echo ""
    print_color $YELLOW "Examples:"
    print_color $CYAN "  ./test-withdrawal-race-conditions.sh concurrent local"
    print_color $CYAN "  ./test-withdrawal-race-conditions.sh database docker"
    print_color $CYAN "  ./test-withdrawal-race-conditions.sh both local"
    echo ""
}

# Function to check if backend directory exists
check_backend() {
    if [ ! -d "backend" ]; then
        print_color $RED "❌ Backend directory not found. Please run from project root."
        exit 1
    fi
}

# Function to check Docker if needed
check_docker() {
    if [ "$2" = "docker" ]; then
        if ! docker info > /dev/null 2>&1; then
            print_color $RED "❌ Docker is not running. Please start Docker first."
            exit 1
        fi
        
        if ! docker-compose -f docker-compose.dev.yml ps backend | grep -q "Up"; then
            print_color $YELLOW "⚠️ Backend container is not running. Starting it first..."
            docker-compose -f docker-compose.dev.yml up -d backend
            
            print_color $CYAN "⏳ Waiting for backend to be ready..."
            sleep 10
        fi
    fi
}

# Function to run concurrent withdrawal test
run_concurrent_test() {
    local env=$1
    print_color $PURPLE "🚀 Running Concurrent Withdrawal Test ($env)..."
    echo ""

    if [ "$env" = "local" ]; then
        cd backend
        npm run test:concurrent-withdrawals:local
        local result=$?
        cd ..
    elif [ "$env" = "docker" ]; then
        docker-compose -f docker-compose.dev.yml exec backend npm run test:concurrent-withdrawals:docker
        local result=$?
    else
        print_color $RED "❌ Invalid environment: $env"
        return 1
    fi

    if [ $result -eq 0 ]; then
        print_color $GREEN "✅ Concurrent withdrawal test passed"
    else
        print_color $RED "❌ Concurrent withdrawal test failed"
    fi

    return $result
}

# Function to run database race condition test
run_database_test() {
    local env=$1
    print_color $PURPLE "🔄 Running Database Race Condition Test ($env)..."
    echo ""

    if [ "$env" = "local" ]; then
        cd backend
        npm run test:db-race-conditions:local
        local result=$?
        cd ..
    elif [ "$env" = "docker" ]; then
        docker-compose -f docker-compose.dev.yml exec backend npm run test:db-race-conditions:docker
        local result=$?
    else
        print_color $RED "❌ Invalid environment: $env"
        return 1
    fi

    if [ $result -eq 0 ]; then
        print_color $GREEN "✅ Database race condition test passed"
    else
        print_color $RED "❌ Database race condition test failed"
    fi

    return $result
}

# Function to show pre-test information
show_pre_test_info() {
    print_color $YELLOW "📋 Before running these tests:"
    print_color $BLUE "  • Make sure your backend is running"
    print_color $BLUE "  • Ensure database is accessible"
    print_color $BLUE "  • These tests will create temporary test data"
    print_color $BLUE "  • Tests will clean up after completion"
    print_color $BLUE "  • Tests may take several minutes to complete"
    echo ""
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_color $YELLOW "Tests cancelled."
        exit 0
    fi
}

# Function to show post-test information
show_post_test_info() {
    local concurrent_result=$1
    local database_result=$2
    
    echo ""
    print_color $GREEN "✅ Race condition tests completed!"
    echo ""
    print_color $YELLOW "📊 Test Results:"
    
    if [ "$concurrent_result" = "0" ]; then
        print_color $GREEN "  • Concurrent withdrawal test: PASSED"
    elif [ "$concurrent_result" = "1" ]; then
        print_color $RED "  • Concurrent withdrawal test: FAILED"
    else
        print_color $BLUE "  • Concurrent withdrawal test: SKIPPED"
    fi
    
    if [ "$database_result" = "0" ]; then
        print_color $GREEN "  • Database race condition test: PASSED"
    elif [ "$database_result" = "1" ]; then
        print_color $RED "  • Database race condition test: FAILED"
    else
        print_color $BLUE "  • Database race condition test: SKIPPED"
    fi
    
    echo ""
    print_color $CYAN "💡 Next steps:"
    print_color $BLUE "  • Review test output for any warnings"
    print_color $BLUE "  • Check logs for detailed error information"
    print_color $BLUE "  • Fix any identified race conditions"
    print_color $BLUE "  • Re-run tests to verify fixes"
    echo ""
}

# Main script logic
check_backend

test_type=${1:-help}
environment=${2:-local}

case "$test_type" in
    "help")
        show_usage
        exit 0
        ;;
    "concurrent"|"database"|"both")
        # Valid test types
        ;;
    *)
        print_color $RED "❌ Invalid test type: $test_type"
        show_usage
        exit 1
        ;;
esac

case "$environment" in
    "local"|"docker")
        # Valid environments
        ;;
    *)
        print_color $RED "❌ Invalid environment: $environment"
        show_usage
        exit 1
        ;;
esac

print_header
show_pre_test_info
check_docker "$test_type" "$environment"

concurrent_result=999
database_result=999

case "$test_type" in
    "concurrent")
        run_concurrent_test "$environment"
        concurrent_result=$?
        ;;
    "database")
        run_database_test "$environment"
        database_result=$?
        ;;
    "both")
        run_concurrent_test "$environment"
        concurrent_result=$?
        
        echo ""
        print_color $CYAN "🔄 Moving to next test..."
        echo ""
        
        run_database_test "$environment"
        database_result=$?
        ;;
esac

show_post_test_info "$concurrent_result" "$database_result"

# Exit with error if any test failed
if [ "$concurrent_result" = "1" ] || [ "$database_result" = "1" ]; then
    exit 1
fi

exit 0
