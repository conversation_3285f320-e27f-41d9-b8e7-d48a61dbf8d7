# 💰 **ENHANCED INTEREST CALCULATION GUIDE**

## 📋 **OVERVIEW**

Enhanced script sử dụng **cùng logic với `cronService.setupDailyInterestCalculation()`** nhưng chạy ngay lập tức thay vì theo lịch trình. Script này cung cấp đầy đủ tính năng của cron job bao gồm transaction safety, wallet updates, và comprehensive logging.

---

## 🚀 **QUICK START**

### **Windows:**
```bash
# Dry run (test without saving)
run-calculate-earnings.bat local

# Actual run (saves to database)
run-calculate-earnings.bat local

# Docker environment
run-calculate-earnings.bat docker
```

### **Linux/Mac:**
```bash
# Make script executable
chmod +x run-calculate-earnings.sh

# Dry run (test without saving)
./run-calculate-earnings.sh local

# Actual run (saves to database)
./run-calculate-earnings.sh local

# Docker environment
./run-calculate-earnings.sh docker
```

---

## 🔧 **AVAILABLE COMMANDS**

### **NPM Scripts:**
| Script | Description | Environment |
|--------|-------------|-------------|
| `npm run calculate-earnings` | Run with default environment | Production |
| `npm run calculate-earnings:local` | Run with .env.local | Development |
| `npm run calculate-earnings:docker` | Run in Docker container | Docker |
| `npm run calculate-earnings:dry-run` | Test run without saving | Production |
| `npm run calculate-earnings:dry-run:local` | Test run with .env.local | Development |
| `npm run calculate-earnings:dry-run:docker` | Test run in Docker | Docker |

### **Helper Scripts:**
| Command | Description |
|---------|-------------|
| `run-calculate-earnings.bat local` | Windows - Run locally |
| `run-calculate-earnings.bat docker` | Windows - Run in Docker |
| `./run-calculate-earnings.sh local` | Linux/Mac - Run locally |
| `./run-calculate-earnings.sh docker` | Linux/Mac - Run in Docker |

---

## 💡 **WHAT THE ENHANCED SCRIPT DOES**

### **🔍 Enhanced Process Overview (Same as cronService):**
1. **Uses `interestCalculationService.processAllActivePackages()`** - identical to cron job
2. **Full transaction safety** with rollback support
3. **Atomic database operations** with MongoDB sessions
4. **Wallet interest balance updates** for withdrawals
5. **Transaction record creation** for audit trails
6. **Payment history logging** for user records
7. **Comprehensive error handling** and retry logic
8. **System notifications** for monitoring
9. **Detailed logging** with Turkey timezone

### **📊 Enhanced Features:**
- **Batch processing** (50 packages per batch) for performance
- **Retry logic** with exponential backoff
- **Transaction rollback** on failures
- **Real-time USDT value** updates
- **Audit trail creation** for compliance
- **Error aggregation** and reporting

### **🔄 Enhanced Updates:**
- **Investment Package**: `totalEarned`, `dailyInterest`, `activeDays`, `lastCalculatedAt`
- **Wallet Assets**: Interest balance for withdrawals
- **Transactions**: Complete transaction records
- **Payment History**: Detailed payment logs
- **Audit Trails**: Compliance and tracking records

---

## 🧪 **DRY RUN MODE**

### **What is Dry Run?**
- **Tests the script** without making database changes
- **Shows what would be updated** with detailed information
- **Safe to run** multiple times
- **Perfect for testing** before actual execution

### **How to Use Dry Run:**
```bash
# NPM scripts with dry run
npm run calculate-earnings:dry-run:local

# Direct command with flag
ts-node scripts/calculate-existing-earnings.ts --dry-run

# Helper scripts (always ask for confirmation)
run-calculate-earnings.bat local
```

### **Dry Run Output:**
```
🧪 DRY RUN MODE: No changes will be saved to database
🧪 Would update package 507f1f77bcf86cd799439011:
   - User: John Doe (<EMAIL>)
   - Currency: BTC
   - Amount: 1000
   - Days active: 15
   - Previous earned: 0.000000
   - New total earned: 150.000000
   - Daily interest: 10.000000
   - Difference: +150.000000
```

---

## 📊 **OUTPUT EXAMPLES**

### **Successful Run:**
```
🚀 Starting calculation of existing investment earnings...

🔗 Connecting to MongoDB: mongodb://***:***@localhost:27017/cryptoyield
✅ MongoDB connected successfully
🔍 Finding existing active investment packages...
📊 Found 5 active investment packages

✅ Updated package 507f1f77bcf86cd799439011:
   - User: John Doe (<EMAIL>)
   - Currency: BTC
   - Amount: 1000
   - Days active: 15
   - Previous earned: 0.000000
   - New total earned: 150.000000
   - Daily interest: 10.000000
   - Difference: +150.000000

🎉 Successfully updated 5 investment packages with calculated earnings

📈 Summary of packages with earnings:
BTC:
  - Packages: 3
  - Total Invested: 5000.000000 BTC
  - Total Earned: 750.000000 BTC
  - Available for Withdrawal: 750.000000 BTC

ETH:
  - Packages: 2
  - Total Invested: 10.000000 ETH
  - Total Earned: 1.500000 ETH
  - Available for Withdrawal: 1.500000 ETH

✅ Calculation completed successfully!
💡 You can now check your withdrawal balances in the frontend
```

### **No Packages Found:**
```
🔍 Finding existing active investment packages...
📊 Found 0 active investment packages
ℹ️ No active investment packages found
✅ Calculation completed successfully!
```

---

## 🔧 **ENVIRONMENT SETUP**

### **Required Environment Variables:**
```bash
# Database connection
MONGO_URI=*******************************************************
# or
MONGODB_URI=*******************************************************

# For Docker environment (.env.docker)
MONGO_URI=**********************************************************************************************
```

### **Environment Files:**
- **`.env.local`**: Local development
- **`.env.docker`**: Docker environment
- **`.env`**: Production environment

---

## 🐳 **DOCKER USAGE**

### **Prerequisites:**
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Or use debug helper
debug-docker.bat start
```

### **Run in Docker:**
```bash
# Using helper script
run-calculate-earnings.bat docker

# Direct Docker command
docker-compose -f docker-compose.dev.yml exec backend npm run calculate-earnings:docker

# Dry run in Docker
docker-compose -f docker-compose.dev.yml exec backend npm run calculate-earnings:dry-run:docker
```

---

## ⚠️ **IMPORTANT NOTES**

### **🔒 Safety Measures:**
- **Always run dry-run first** to see what will be changed
- **Backup database** before running in production
- **Test in development** environment first
- **Verify results** after execution

### **📋 Prerequisites:**
- **Active investment packages** must exist in database
- **Database connection** must be accessible
- **Proper environment variables** must be set
- **Node.js and TypeScript** must be installed

### **🎯 Best Practices:**
1. **Run dry-run first**: `npm run calculate-earnings:dry-run:local`
2. **Check output carefully**: Verify calculations are correct
3. **Run actual script**: `npm run calculate-earnings:local`
4. **Verify in frontend**: Check withdrawal balances
5. **Test withdrawals**: Ensure earnings are available

---

## 🔍 **TROUBLESHOOTING**

### **Common Issues:**

#### **1. MongoDB Connection Failed**
```bash
❌ MongoDB connection failed: MongoNetworkError
```
**Solution:**
- Check `MONGO_URI` environment variable
- Ensure MongoDB is running
- Verify credentials and network access

#### **2. No Active Packages Found**
```bash
ℹ️ No active investment packages found
```
**Solution:**
- Check if investment packages exist in database
- Verify package status is 'active'
- Create test packages if needed

#### **3. Import Path Errors**
```bash
❌ Cannot find module '../src/models/investmentPackageModel'
```
**Solution:**
- Run from backend directory: `cd backend`
- Use npm scripts instead of direct ts-node
- Check TypeScript configuration

#### **4. Permission Errors**
```bash
❌ Permission denied
```
**Solution:**
- Make scripts executable: `chmod +x run-calculate-earnings.sh`
- Run with proper permissions
- Check file ownership

### **Debug Commands:**
```bash
# Check environment variables
echo $MONGO_URI

# Test database connection
mongosh $MONGO_URI

# Check active packages
mongosh $MONGO_URI --eval "db.investmentpackages.find({status:'active'}).count()"

# View script logs
npm run calculate-earnings:local 2>&1 | tee earnings.log
```

---

## 📈 **INTEGRATION WITH FRONTEND**

### **After Running Script:**
1. **Earnings are calculated** and stored in database
2. **totalEarned field** is updated for each package
3. **Withdrawal balances** are automatically available
4. **Frontend APIs** will show updated balances
5. **Users can withdraw** their accumulated earnings

### **Frontend Integration Points:**
- **Withdrawal page**: Shows available earnings
- **Wallet balances**: Displays interest earnings
- **Transaction history**: Records earnings distribution
- **User dashboard**: Shows total accumulated interest

---

## 🎯 **PRODUCTION DEPLOYMENT**

### **Production Checklist:**
- [ ] **Backup database** before running
- [ ] **Test in staging** environment first
- [ ] **Run dry-run** to verify calculations
- [ ] **Schedule during low traffic** period
- [ ] **Monitor system** during execution
- [ ] **Verify results** after completion
- [ ] **Test withdrawal** functionality

### **Automation Options:**
```bash
# Cron job for daily earnings calculation
0 2 * * * cd /path/to/backend && npm run calculate-earnings >> /var/log/earnings.log 2>&1

# Docker cron job
0 2 * * * docker-compose -f docker-compose.yml exec backend npm run calculate-earnings
```

---

## 📞 **SUPPORT**

### **Quick Reference:**
```bash
# Test run
run-calculate-earnings.bat local

# Actual run
npm run calculate-earnings:local

# Docker run
run-calculate-earnings.bat docker

# Get help
run-calculate-earnings.bat help
```

**🎉 Your investment earnings calculation system is ready for production use!**
