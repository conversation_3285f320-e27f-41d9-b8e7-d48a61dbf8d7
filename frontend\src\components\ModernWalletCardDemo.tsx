import React from 'react';
import { Box, VStack, SimpleGrid, Text, Badge, Divider } from '@chakra-ui/react';
import ModernWalletCard from './ModernWalletCard';
import { useCryptoPrices } from '../hooks/useCryptoPrices';

/**
 * Demo component để test ModernWalletCard với real crypto prices
 */
const ModernWalletCardDemo: React.FC = () => {
  const { prices, isLoading, error, cacheInfo } = useCryptoPrices();

  // Sample wallet assets data
  const sampleAssets = [
    {
      symbol: 'BTC',
      balance: 0.5,
      interestBalance: 0.025,
      commissionBalance: 0.01,
      principalAmount: 0.5,
      dailyInterestRate: 0.01,
      activePackages: 2,
      isLocked: false,
      daysUntilUnlock: 0
    },
    {
      symbol: 'ETH',
      balance: 2.5,
      interestBalance: 0.15,
      commissionBalance: 0.05,
      principalAmount: 2.5,
      dailyInterestRate: 0.012,
      activePackages: 1,
      isLocked: true,
      daysUntilUnlock: 15
    },
    {
      symbol: 'USDT',
      balance: 1000,
      interestBalance: 50,
      commissionBalance: 25,
      principalAmount: 1000,
      dailyInterestRate: 0.008,
      activePackages: 3,
      isLocked: false,
      daysUntilUnlock: 0
    },
    {
      symbol: 'BNB',
      balance: 5,
      interestBalance: 0.3,
      commissionBalance: 0.1,
      principalAmount: 5,
      dailyInterestRate: 0.015,
      activePackages: 1,
      isLocked: false,
      daysUntilUnlock: 0
    }
  ];

  const handleDeposit = (currency: string, walletData: any) => {
    console.log('💰 Deposit clicked for:', currency, walletData);
  };

  const handleWithdraw = (currency: string, walletData: any) => {
    console.log('💸 Withdraw clicked for:', currency, walletData);
  };

  return (
    <Box p={6} bg="gray.900" minH="100vh">
      <VStack spacing={6} align="stretch" maxW="1200px" mx="auto">
        {/* Header */}
        <Box textAlign="center" mb={6}>
          <Text fontSize="2xl" fontWeight="bold" color="white" mb={2}>
            💳 Modern Wallet Cards with Real Crypto Prices
          </Text>
          <Text color="gray.400" fontSize="md">
            Demonstrating real-time price integration with cached crypto data
          </Text>
        </Box>

        {/* Price Cache Status */}
        <Box p={4} bg="gray.800" borderRadius="lg" borderWidth="1px" borderColor="gray.700">
          <Text fontSize="lg" fontWeight="bold" color="white" mb={3}>
            📊 Price Cache Status
          </Text>
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
            <Box>
              <Text fontSize="xs" color="gray.400">Status</Text>
              <Badge colorScheme={isLoading ? 'yellow' : error ? 'red' : 'green'}>
                {isLoading ? 'Loading' : error ? 'Error' : 'Active'}
              </Badge>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.400">Prices Loaded</Text>
              <Text color="white" fontWeight="bold">{Object.keys(prices).length}</Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.400">Cache Valid</Text>
              <Badge colorScheme={cacheInfo.isValid ? 'green' : 'red'}>
                {cacheInfo.isValid ? 'Yes' : 'No'}
              </Badge>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.400">Cache Age</Text>
              <Text color="white" fontWeight="bold">
                {Math.round(cacheInfo.cacheAge / 1000)}s
              </Text>
            </Box>
          </SimpleGrid>
          
          {error && (
            <Box mt={3} p={3} bg="red.900" borderRadius="md" borderWidth="1px" borderColor="red.700">
              <Text color="red.200" fontSize="sm">
                <strong>Error:</strong> {error}
              </Text>
            </Box>
          )}
        </Box>

        {/* Current Prices Display */}
        <Box p={4} bg="gray.800" borderRadius="lg" borderWidth="1px" borderColor="gray.700">
          <Text fontSize="lg" fontWeight="bold" color="white" mb={3}>
            💰 Current Crypto Prices
          </Text>
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
            {sampleAssets.map(asset => {
              const price = prices[asset.symbol] || 0;
              return (
                <Box key={asset.symbol} p={3} bg="gray.700" borderRadius="md">
                  <Text fontSize="sm" color="gray.300">{asset.symbol}</Text>
                  <Text color="white" fontWeight="bold">
                    ${price.toLocaleString(undefined, { 
                      minimumFractionDigits: 2, 
                      maximumFractionDigits: 6 
                    })}
                  </Text>
                </Box>
              );
            })}
          </SimpleGrid>
        </Box>

        <Divider borderColor="gray.700" />

        {/* Wallet Cards Grid */}
        <Box>
          <Text fontSize="lg" fontWeight="bold" color="white" mb={4}>
            🏦 Wallet Cards with Real Price Integration
          </Text>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 2 }} spacing={6}>
            {sampleAssets.map(asset => (
              <ModernWalletCard
                key={asset.symbol}
                asset={asset}
                onDeposit={handleDeposit}
                onWithdraw={handleWithdraw}
              />
            ))}
          </SimpleGrid>
        </Box>

        {/* Features Explanation */}
        <Box p={4} bg="blue.900" borderRadius="lg" borderWidth="1px" borderColor="blue.700">
          <Text fontSize="lg" fontWeight="bold" color="blue.100" mb={3}>
            ✨ Features Demonstrated
          </Text>
          <VStack align="start" spacing={2}>
            <Text color="blue.200" fontSize="sm">
              • <strong>Real-time USD conversion:</strong> Uses cached crypto prices for accurate USD values
            </Text>
            <Text color="blue.200" fontSize="sm">
              • <strong>Smart caching:</strong> Prices cached for 5 minutes to reduce API calls
            </Text>
            <Text color="blue.200" fontSize="sm">
              • <strong>Loading states:</strong> Shows loading indicators while fetching prices
            </Text>
            <Text color="blue.200" fontSize="sm">
              • <strong>Error handling:</strong> Graceful fallback when prices unavailable
            </Text>
            <Text color="blue.200" fontSize="sm">
              • <strong>Multiple currencies:</strong> Supports BTC, ETH, USDT, BNB, and more
            </Text>
            <Text color="blue.200" fontSize="sm">
              • <strong>Detailed breakdown:</strong> Shows USD values for balance, interest, and commissions
            </Text>
          </VStack>
        </Box>

        {/* Usage Instructions */}
        <Box p={4} bg="green.900" borderRadius="lg" borderWidth="1px" borderColor="green.700">
          <Text fontSize="lg" fontWeight="bold" color="green.100" mb={3}>
            📝 How to Use
          </Text>
          <VStack align="start" spacing={2}>
            <Text color="green.200" fontSize="sm">
              1. <strong>Import the component:</strong> <code>import ModernWalletCard from './components/ModernWalletCard'</code>
            </Text>
            <Text color="green.200" fontSize="sm">
              2. <strong>Provide asset data:</strong> Pass wallet asset object with balance, symbol, etc.
            </Text>
            <Text color="green.200" fontSize="sm">
              3. <strong>Handle actions:</strong> Provide onDeposit and onWithdraw callback functions
            </Text>
            <Text color="green.200" fontSize="sm">
              4. <strong>Automatic pricing:</strong> USD values calculated automatically using real crypto prices
            </Text>
            <Text color="green.200" fontSize="sm">
              5. <strong>Cache benefits:</strong> Subsequent loads use cached prices for instant display
            </Text>
          </VStack>
        </Box>
      </VStack>
    </Box>
  );
};

export default ModernWalletCardDemo;
