#!/bin/bash

# 🧪 Test Calculate Earnings Script
# Quick test script for calculate-existing-earnings in Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_color() {
    printf "${1}${2}${NC}\n"
}

print_color $CYAN "🧪 Testing Calculate Earnings Script in Docker"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_color $RED "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if backend container is running
if ! docker-compose -f docker-compose.dev.yml ps backend | grep -q "Up"; then
    print_color $YELLOW "⚠️ Backend container is not running. Starting it first..."
    docker-compose -f docker-compose.dev.yml up -d backend
    
    print_color $CYAN "⏳ Waiting for backend to be ready..."
    sleep 15
fi

print_color $GREEN "🚀 Running dry-run test in Docker..."
echo ""

# Run dry-run test
docker-compose -f docker-compose.dev.yml exec backend npm run calculate-earnings:dry-run:docker

echo ""
print_color $GREEN "✅ Dry-run test completed!"
echo ""

read -p "Do you want to run the actual calculation? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_color $YELLOW "🔄 Running actual calculation..."
    echo ""
    
    docker-compose -f docker-compose.dev.yml exec backend npm run calculate-earnings:docker
    
    echo ""
    print_color $GREEN "✅ Calculation completed!"
    print_color $CYAN "💡 Check the frontend withdrawal page to verify earnings"
else
    print_color $YELLOW "Operation cancelled."
fi
