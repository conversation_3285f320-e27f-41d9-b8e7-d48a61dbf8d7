/**
 * Transaction Status Badge Component
 * Displays transaction status with consistent styling, tooltips, and descriptions
 */

import React from 'react';
import {
  Badge,
  Tooltip,
  HStack,
  Icon,
  Text,
  Box
} from '@chakra-ui/react';
import { 
  FaClock, 
  FaCheck, 
  FaCheckCircle, 
  FaTimes, 
  FaExclamationTriangle,
  FaQuestion
} from 'react-icons/fa';
import { 
  getStatusColorScheme, 
  getStatusLabel, 
  getStatusDescription,
  getStatusBadgeProps,
  getTransactionTypeStatusMessage 
} from '../utils/transactionStatusUtils';

interface TransactionStatusBadgeProps {
  status: string;
  transactionType?: string;
  showIcon?: boolean;
  showTooltip?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'subtle' | 'solid' | 'outline';
  customDescription?: string;
}

const TransactionStatusBadge: React.FC<TransactionStatusBadgeProps> = ({
  status,
  transactionType = 'transaction',
  showIcon = true,
  showTooltip = true,
  size = 'md',
  variant = 'subtle',
  customDescription
}) => {
  // Get status configuration
  const statusConfig = getStatusBadgeProps(status);
  const statusLabel = getStatusLabel(status);
  const statusDescription = customDescription || 
    (transactionType ? getTransactionTypeStatusMessage(transactionType, status) : getStatusDescription(status));

  // Get appropriate icon
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return FaClock;
      case 'approved':
        return FaCheck;
      case 'completed':
        return FaCheckCircle;
      case 'rejected':
        return FaTimes;
      case 'failed':
        return FaExclamationTriangle;
      default:
        return FaQuestion;
    }
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      fontSize: 'xs',
      px: 2,
      py: 1,
      iconSize: 3
    },
    md: {
      fontSize: 'sm',
      px: 3,
      py: 1,
      iconSize: 4
    },
    lg: {
      fontSize: 'md',
      px: 4,
      py: 2,
      iconSize: 5
    }
  };

  const currentSize = sizeConfig[size];

  const badgeContent = (
    <Badge
      colorScheme={getStatusColorScheme(status)}
      variant={variant}
      fontSize={currentSize.fontSize}
      px={currentSize.px}
      py={currentSize.py}
      borderRadius="md"
      textTransform="uppercase"
      fontWeight="medium"
      display="flex"
      alignItems="center"
      gap={showIcon ? 1 : 0}
    >
      {showIcon && (
        <Icon 
          as={getStatusIcon(status)} 
          boxSize={currentSize.iconSize}
        />
      )}
      <Text>{statusLabel}</Text>
    </Badge>
  );

  if (!showTooltip) {
    return badgeContent;
  }

  return (
    <Tooltip
      label={statusDescription}
      placement="top"
      hasArrow
      bg="gray.700"
      color="white"
      fontSize="sm"
      px={3}
      py={2}
      borderRadius="md"
      maxW="300px"
      textAlign="center"
    >
      {badgeContent}
    </Tooltip>
  );
};

export default TransactionStatusBadge;
