import axios from 'axios';

// Interface cho dữ liệu gi<PERSON> tiền tệ
export interface CryptoPriceData {
  [symbol: string]: number;
}

export interface CachedPriceData {
  prices: CryptoPriceData;
  timestamp: number;
  expiresAt: number;
}

// Configuration
const CACHE_KEY = 'crypto_prices_cache';
const CACHE_DURATION = 5 * 60 * 1000; // 5 phút
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

class CryptoPriceService {
  private static instance: CryptoPriceService;
  private prices: CryptoPriceData = {};
  private lastFetch: number = 0;
  private isLoading: boolean = false;
  private subscribers: Array<(prices: CryptoPriceData) => void> = [];

  private constructor() {
    this.loadFromCache();
  }

  public static getInstance(): CryptoPriceService {
    if (!CryptoPriceService.instance) {
      CryptoPriceService.instance = new CryptoPriceService();
    }
    return CryptoPriceService.instance;
  }

  /**
   * Subscribe to price updates
   */
  public subscribe(callback: (prices: CryptoPriceData) => void): () => void {
    this.subscribers.push(callback);
    
    // Immediately call with current prices if available
    if (Object.keys(this.prices).length > 0) {
      callback(this.prices);
    }

    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  /**
   * Notify all subscribers of price updates
   */
  private notifySubscribers(): void {
    this.subscribers.forEach(callback => {
      try {
        callback(this.prices);
      } catch (error) {
        console.error('Error in price update subscriber:', error);
      }
    });
  }

  /**
   * Load prices from localStorage cache
   */
  private loadFromCache(): void {
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (cached) {
        const cachedData: CachedPriceData = JSON.parse(cached);
        
        // Check if cache is still valid
        if (Date.now() < cachedData.expiresAt) {
          this.prices = cachedData.prices;
          this.lastFetch = cachedData.timestamp;
          console.log('💰 CryptoPriceService: Loaded prices from cache', this.prices);
          this.notifySubscribers();
          return;
        } else {
          console.log('💰 CryptoPriceService: Cache expired, will fetch fresh data');
          localStorage.removeItem(CACHE_KEY);
        }
      }
    } catch (error) {
      console.error('💰 CryptoPriceService: Error loading from cache:', error);
      localStorage.removeItem(CACHE_KEY);
    }
  }

  /**
   * Save prices to localStorage cache
   */
  private saveToCache(): void {
    try {
      const cacheData: CachedPriceData = {
        prices: this.prices,
        timestamp: this.lastFetch,
        expiresAt: Date.now() + CACHE_DURATION
      };
      
      localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
      console.log('💰 CryptoPriceService: Saved prices to cache');
    } catch (error) {
      console.error('💰 CryptoPriceService: Error saving to cache:', error);
    }
  }

  /**
   * Check if cache is still valid
   */
  private isCacheValid(): boolean {
    return Date.now() - this.lastFetch < CACHE_DURATION;
  }

  /**
   * Fetch prices from API
   */
  private async fetchPricesFromAPI(): Promise<CryptoPriceData> {
    try {
      console.log('💰 CryptoPriceService: Fetching prices from API...');
      
      const response = await axios.get(`${API_BASE_URL}/crypto/prices`, {
        timeout: 10000, // 10 second timeout
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data && response.data.status === 'success') {
        return response.data.data;
      } else {
        throw new Error('Invalid API response format');
      }
    } catch (error: any) {
      console.error('💰 CryptoPriceService: API fetch failed:', error);
      
      // Return fallback prices if API fails
      const fallbackPrices: CryptoPriceData = {
        'BTC': 106238,
        'ETH': 2615,
        'USDT': 1,
        'BNB': 667.21,
        'SOL': 153.01,
        'TRX': 0.27,
        'DOGE': 0.2
      };
      
      console.log('💰 CryptoPriceService: Using fallback prices');
      return fallbackPrices;
    }
  }

  /**
   * Get current prices (from cache or fetch if needed)
   */
  public async getPrices(forceRefresh: boolean = false): Promise<CryptoPriceData> {
    // If we have valid cached data and not forcing refresh, return it
    if (!forceRefresh && this.isCacheValid() && Object.keys(this.prices).length > 0) {
      console.log('💰 CryptoPriceService: Returning cached prices');
      return this.prices;
    }

    // If already loading, wait for it to complete
    if (this.isLoading) {
      console.log('💰 CryptoPriceService: Already loading, waiting...');
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!this.isLoading) {
            resolve(this.prices);
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }

    // Fetch fresh data
    this.isLoading = true;
    
    try {
      const freshPrices = await this.fetchPricesFromAPI();
      this.prices = freshPrices;
      this.lastFetch = Date.now();
      this.saveToCache();
      this.notifySubscribers();
      
      console.log('💰 CryptoPriceService: Updated prices:', this.prices);
      return this.prices;
    } catch (error) {
      console.error('💰 CryptoPriceService: Error fetching prices:', error);
      
      // Return cached prices if available, even if expired
      if (Object.keys(this.prices).length > 0) {
        console.log('💰 CryptoPriceService: Returning stale cached prices due to error');
        return this.prices;
      }
      
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Get price for a specific cryptocurrency
   */
  public async getPrice(symbol: string): Promise<number> {
    const prices = await this.getPrices();
    const normalizedSymbol = symbol.toUpperCase();
    return prices[normalizedSymbol] || 0;
  }

  /**
   * Convert amount from one crypto to USD
   */
  public async convertToUSD(amount: number, symbol: string): Promise<number> {
    const price = await this.getPrice(symbol);
    return amount * price;
  }

  /**
   * Convert amount from USD to crypto
   */
  public async convertFromUSD(usdAmount: number, symbol: string): Promise<number> {
    const price = await this.getPrice(symbol);
    return price > 0 ? usdAmount / price : 0;
  }

  /**
   * Force refresh prices from API
   */
  public async refreshPrices(): Promise<CryptoPriceData> {
    return this.getPrices(true);
  }

  /**
   * Clear cache and reset
   */
  public clearCache(): void {
    localStorage.removeItem(CACHE_KEY);
    this.prices = {};
    this.lastFetch = 0;
    console.log('💰 CryptoPriceService: Cache cleared');
  }

  /**
   * Get cache info for debugging
   */
  public getCacheInfo(): {
    hasCache: boolean;
    cacheAge: number;
    isValid: boolean;
    priceCount: number;
  } {
    return {
      hasCache: Object.keys(this.prices).length > 0,
      cacheAge: Date.now() - this.lastFetch,
      isValid: this.isCacheValid(),
      priceCount: Object.keys(this.prices).length
    };
  }
}

// Export singleton instance
export const cryptoPriceService = CryptoPriceService.getInstance();
export default cryptoPriceService;
