#!/bin/bash

# 🐳 CryptoYield Backend Debug Helper Script
# This script helps manage Docker debugging for the backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

# Function to print header
print_header() {
    echo ""
    print_color $CYAN "🐳 =================================="
    print_color $CYAN "   CryptoYield Backend Debug Helper"
    print_color $CYAN "🐳 =================================="
    echo ""
}

# Function to show usage
show_usage() {
    print_header
    echo "Usage: $0 [COMMAND]"
    echo ""
    print_color $YELLOW "Available Commands:"
    echo ""
    print_color $GREEN "  start           🚀 Start development environment with hot reload"
    print_color $GREEN "  debug           🐛 Start with debug inspector (port 9229)"
    print_color $GREEN "  debug-break     🔍 Start with debug breakpoints (port 9230)"
    print_color $GREEN "  stop            🛑 Stop all development containers"
    print_color $GREEN "  restart         🔄 Restart backend container"
    print_color $GREEN "  logs            📋 Show backend logs"
    print_color $GREEN "  logs-follow     📋 Follow backend logs in real-time"
    print_color $GREEN "  shell           💻 Open shell in backend container"
    print_color $GREEN "  status          📊 Show container status"
    print_color $GREEN "  clean           🧹 Clean up containers and volumes"
    print_color $GREEN "  help            ❓ Show this help message"
    echo ""
    print_color $YELLOW "Debug Ports:"
    print_color $BLUE "  Backend API:     http://localhost:5000"
    print_color $BLUE "  Debug Inspector: chrome://inspect (port 9229)"
    print_color $BLUE "  Debug Break:     chrome://inspect (port 9230)"
    print_color $BLUE "  MongoDB:         mongodb://localhost:27017"
    print_color $BLUE "  Mongo Express:   http://localhost:8081"
    print_color $BLUE "  Redis:           redis://localhost:6379"
    echo ""
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_color $RED "❌ Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to start development environment
start_dev() {
    print_color $GREEN "🚀 Starting development environment..."
    docker-compose -f docker-compose.dev.yml up -d
    print_color $GREEN "✅ Development environment started!"
    echo ""
    print_color $YELLOW "🔗 Available Services:"
    print_color $BLUE "  Backend API:     http://localhost:5000"
    print_color $BLUE "  Debug Inspector: chrome://inspect (port 9229)"
    print_color $BLUE "  MongoDB:         mongodb://localhost:27017"
    print_color $BLUE "  Mongo Express:   http://localhost:8081 (admin/admin123)"
    print_color $BLUE "  Redis:           redis://localhost:6379"
    echo ""
    print_color $CYAN "💡 To debug: Open Chrome -> chrome://inspect -> Configure -> Add localhost:9229"
}

# Function to start with debug breakpoints
start_debug_break() {
    print_color $PURPLE "🔍 Starting backend with debug breakpoints..."
    docker-compose -f docker-compose.dev.yml --profile debug up -d backend-debug
    print_color $GREEN "✅ Backend debug started with breakpoints!"
    echo ""
    print_color $YELLOW "🔗 Debug Services:"
    print_color $BLUE "  Backend API:     http://localhost:5001"
    print_color $BLUE "  Debug Inspector: chrome://inspect (port 9230)"
    echo ""
    print_color $CYAN "💡 The backend will wait for debugger to attach before starting."
    print_color $CYAN "💡 Open Chrome -> chrome://inspect -> Configure -> Add localhost:9230"
}

# Function to show logs
show_logs() {
    print_color $CYAN "📋 Showing backend logs..."
    docker-compose -f docker-compose.dev.yml logs backend
}

# Function to follow logs
follow_logs() {
    print_color $CYAN "📋 Following backend logs (Ctrl+C to stop)..."
    docker-compose -f docker-compose.dev.yml logs -f backend
}

# Function to open shell
open_shell() {
    print_color $CYAN "💻 Opening shell in backend container..."
    docker-compose -f docker-compose.dev.yml exec backend /bin/bash
}

# Function to show status
show_status() {
    print_color $CYAN "📊 Container Status:"
    docker-compose -f docker-compose.dev.yml ps
    echo ""
    print_color $CYAN "📊 Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $(docker-compose -f docker-compose.dev.yml ps -q) 2>/dev/null || echo "No containers running"
}

# Function to restart backend
restart_backend() {
    print_color $YELLOW "🔄 Restarting backend container..."
    docker-compose -f docker-compose.dev.yml restart backend
    print_color $GREEN "✅ Backend restarted!"
}

# Function to stop containers
stop_containers() {
    print_color $YELLOW "🛑 Stopping development containers..."
    docker-compose -f docker-compose.dev.yml down
    print_color $GREEN "✅ Containers stopped!"
}

# Function to clean up
clean_up() {
    print_color $YELLOW "🧹 Cleaning up containers and volumes..."
    docker-compose -f docker-compose.dev.yml down -v --remove-orphans
    docker system prune -f
    print_color $GREEN "✅ Cleanup completed!"
}

# Main script logic
check_docker

case "${1:-help}" in
    "start")
        start_dev
        ;;
    "debug")
        start_dev
        print_color $PURPLE "🐛 Debug inspector available on port 9229"
        ;;
    "debug-break")
        start_debug_break
        ;;
    "stop")
        stop_containers
        ;;
    "restart")
        restart_backend
        ;;
    "logs")
        show_logs
        ;;
    "logs-follow")
        follow_logs
        ;;
    "shell")
        open_shell
        ;;
    "status")
        show_status
        ;;
    "clean")
        clean_up
        ;;
    "help"|*)
        show_usage
        ;;
esac
